import requests
from fuzzywuzzy import fuzz

# URL du service web
url = "http://127.0.0.1:8000/api/client/ask/1/bot/1"

# Liste des questions et des mots clés de réponse attendue
questions_and_expected_keywords = [
    
    ("Qu'est-ce que le menu pour ajouter des véhicules ?", ["Menu", "Edition", "véhicules"]),
    ("Comment fonctionne la fonctionnalité de recherche rapide ?", ["recherche", "rapide", "mots-clés"]),
    ("Que contient le menu Tableau de Bord ?", ["Tableau", "Bord", "opérations", "clés"]),
    ("Comment ajouter un nouveau véhicule au système ?", ["ajouter", "nouveau", "véhicule", "Enregistrer"]),
    ("Quels détails peuvent être modifiés dans le menu Edition véhicules ?", ["marque", "modèle", "plaque", "état"]),
    ("Comment suivre l'entretien des véhicules dans le système ?", ["Cahier", "entretien", "services", "réparations"]),
    ("Que montre le menu Véhicules en service ?", ["Véhicules", "service", "location"]),
    ("Comment gérer les véhicules hors service ?", ["Véhicules", "hors service", "réparations", "entretien"]),
    ("Comment gérer les véhicules de partenaires ?", ["Véhicules", "partenaires", "partages", "revenus"]),
    ("Quels documents des véhicules peuvent être gérés dans le menu Documents des véhicules ?", ["documents", "assurance", "enregistrement", "permis"]),
    ("Comment ajouter et gérer des photos de véhicules ?", ["photos", "Album", "marketing"]),
    ("Que permet de faire le menu Marques & Modèles ?", ["Marques", "Modèles", "ajouter", "modifier"]),
    ("Comment créer et gérer les profils des clients ?", ["Edition", "clients", "nouveaux", "supprimer"]),
    ("Que montre la liste des clients dans le menu Clients & Tiers ?", ["liste", "clients", "profils", "transactions"]),
    ("Comment gérer les documents des clients ?", ["documents", "clients", "télécharger", "gérer"]),
    ("Que contient le menu Nos agents ?", ["Nos agents", "informations", "performances"]),
    ("Comment gérer les conducteurs associés à l'entreprise ?", ["Nos conducteurs", "qualifications", "disponibilités"]),
    ("Comment créer une nouvelle réservation pour un client ?", ["Nouvelle réservation", "véhicule", "dates", "client"]),
    ("Que montre la liste des réservations ?", ["liste", "réservations", "détails", "modifier"]),
    ("Comment accéder à l'historique des réservations ?", ["Historique des Réservations", "précédentes"]),
    ("Que montre le planning des réservations ?", ["planning", "réservations", "calendrier", "véhicules"]),
    ("Quels véhicules sont disponibles pour une location immédiate ?", ["Disponibilité", "immédiate", "véhicules"]),
    ("Comment créer et gérer des contrats de location ?", ["Edition", "contrat", "personnaliser"]),
    ("Que contient le menu Liste de contrats ?", ["Liste de contrats", "consultation", "archiver"]),
    ("Comment gérer les réservations effectuées à travers le site web ou l'application mobile ?", ["Liste des réservations", "site web", "application"]),
    ("Comment gérer les demandes de réservation en attente de confirmation ?", ["Pré-réservations", "attente", "confirmation"]),
    ("Comment gérer les réservations faites par des partenaires commerciaux ?", ["Pré-résa", "partenaires", "coordination"]),
    ("Comment contrôler quels modèles de véhicules sont affichés sur le site web et l'application ?", ["Modèles visibles", "Site", "application"]),
    ("Comment gérer les variations de tarifs selon les saisons ou les périodes spécifiques ?", ["Périodes tarifaires", "variations", "tarifs"]),
    ("Comment créer et modifier différentes catégories de prix pour les véhicules ?", ["Catégories tarifaires", "prix"]),
    ("Comment définir les prix de location pour chaque véhicule ?", ["Tarifs", "location", "véhicule"]),
    ("Comment gérer les frais d'abandon pour les véhicules ?", ["Frais d'abandon", "appliqués"]),
    ("Comment définir les coûts associés au déplacement des véhicules entre succursales ?", ["Frais de Convoyage", "coûts"]),
    ("Comment permettre aux clients de réserver des équipements supplémentaires ?", ["Extra/Accessoires", "réserver"]),
    ("Comment gérer les tarifs pour le kilométrage supplémentaire ?", ["Extra Kilométrage", "tarifs"]),
    ("Comment définir les options de couverture d'assurance pour les clients ?", ["Franchise & rachats", "couverture", "assurance"]),
    ("Comment offrir des packages d'assurance à tarif réduit ?", ["Packs de franchise", "tarif réduit"]),
    ("Comment gérer les codes promotionnels pour les réservations ?", ["Codes Promo", "promotionnels"]),
    ("Comment mettre en place des restrictions ou des modifications tarifaires ?", ["Redu. Maj.", "restrictions", "modifications"]),
    ("Comment collecter et gérer les avis des clients sur leurs expériences de location ?", ["Avis Clients", "collecter", "gérer"]),
    ("Comment gérer les informations concernant les lieux de prise en charge et de retour des véhicules ?", ["Agences et points de livraison", "informations"]),
    ("Comment définir les termes et conditions de location ?", ["Conditions générales", "termes"]),
    ("Que contient le menu FAQs ?", ["FAQs", "réponses", "questions"]),
    ("Comment publier des nouvelles et des promotions sur le site web et l'application ?", ["News & Promos", "publier"]),
    ("Comment afficher la politique de confidentialité de l'entreprise ?", ["Politique de confidentialité", "afficher"]),
    ("Comment assurer la synchronisation des données sur le site web et l'application mobile ?", ["Synchronisation des données", "assurer"]),
    ("Que contient le menu Résumé comptable ?", ["Résumé comptable", "revenus", "dépenses"]),
    ("Comment consulter les revenus et les coûts associés à chaque véhicule ?", ["Résumé par véhicule", "revenus", "coûts"]),
    ("Comment gérer les différents types de facturation ?", ["Facturation", "types", "libre", "réservations"]),
    ("Comment identifier les locations impayées ?", ["Locations impayées", "identifier"]),
    ("Comment suivre les flux de trésorerie de l'entreprise ?", ["Gestion de caisse", "flux", "trésorerie"]),
    ("Comment enregistrer et suivre les coûts spécifiques liés à chaque véhicule ?", ["Frais par véhicule", "coûts", "spécifiques"]),
    ("Comment consulter l'historique des interactions passées avec chaque client ?", ["Historique client", "interactions"]),
    ("Comment tracer l'utilisation et l'entretien de chaque véhicule ?", ["Historique véhicule", "utilisation", "entretien"]),
    ("Comment analyser le coût total de possession et l'amortissement des véhicules ?", ["Amortissement véhicule", "coût", "possession"]),
    ("Comment montrer le pourcentage de temps que chaque véhicule est loué ?", ["Taux de réservation", "pourcentage", "loué"]),
    ("Comment obtenir des statistiques sur le nombre de réservations et d'annulations ?", ["Locations/Annulations", "statistiques"]),
    ("Comment exporter et sauvegarder les données importantes ?", ["Sauvegarde des données", "exporter"]),
    ("Comment créer et personnaliser les modèles de contrats de location ?", ["Modèles de Contrats", "créer", "personnaliser"]),
    ("Comment générer des bons de convoyage pour le transport des véhicules ?", ["Bon de convoyage", "générer"]),
    ("Comment configurer des paramètres globaux pour l'entreprise ?", ["Paramètres généraux", "configurer"]),
    ("Comment créer et gérer les rôles utilisateurs ?", ["Rôles & Permissions", "créer", "gérer"]),
    ("Comment gérer les informations et les accords avec des partenaires commerciaux ?", ["Partenaires", "informations", "accords"]),
    ("Comment mettre à jour et gérer les taux de change pour les transactions ?", ["Taux de Change", "mettre à jour", "gérer"]),
    ("Comment configurer les modes de paiement acceptés par l'entreprise ?", ["Modes de paiement", "configurer"]),
    ("Comment gérer les notifications envoyées aux clients ?", ["Notifications client", "gérer"]),
    ("Comment personnaliser le contenu des notifications envoyées aux clients ?", ["Modèles des notifications", "personnaliser"]),
    ("Comment configurer les paramètres du serveur de messagerie ?", ["Configuration SMTP", "serveur", "messagerie"]),
    ("Que contient le menu Informations Entreprise ?", ["Informations Entreprise", "détails"]),
    ("Comment fusionner des entrées dupliquées dans la base de données des clients ?", ["Fusion clients", "entrées", "dupliquées"]),
    ("Comment gérer l'intégration et les paramètres du compte avec FINOU ?", ["Compte FINOU", "intégration"]),
    ("Comment obtenir une vue d'ensemble en temps réel de la localisation des véhicules ?", ["Tracking GPS", "vue", "temps réel"]),
    ("Comment associer chaque véhicule à son dispositif GPS correspondant ?", ["Correspondances GPS", "associer"]),
    ("Que montre la page Tableau de Bord ?", ["Tableau de Bord", "informations", "fonctionnalités"]),
    ("Comment ajouter un nouveau véhicule à la base de données du système ?", ["ajouter", "nouveau", "véhicule"]),
    ("Quels sont les champs à remplir pour ajouter un véhicule ?", ["champs", "matricule", "marque", "modèle"]),
    ("Quels documents administratifs sont requis pour ajouter un véhicule ?", ["documents administratifs", "assurance", "carte grise"]),
    ("Quelles sont les informations sur l'achat à renseigner pour ajouter un véhicule ?", ["informations sur l'achat", "date", "prix"]),
    ("Quelles sont les actions disponibles après avoir rempli le formulaire d'ajout de véhicule ?", ["actions", "Enregistrer", "créer une copie"]),
    ("Comment valider le formulaire d'ajout de véhicule ?", ["valider", "formulaire", "obligatoires", "dates"])
]

# Fonction pour tester chaque question et comparer les réponses
def test_bot_responses():
    for question, expected_keywords in questions_and_expected_keywords:
        response = ask_bot(question)
        response_text = response.get("response", "").lower()
        
        if all(keyword.lower() in response_text for keyword in expected_keywords):
            print(f"Question: {question}\nResponse: {response_text}\nStatus: PASS\n")
        else:
            print(f"Question: {question}\nResponse: {response_text}\nStatus: FAIL\n")


# Fonction pour tester chaque question et comparer les réponses
def test_bot_responses_fuzzy():
    threshold = 80  # Seuil de similarité pour accepter la réponse
    pass_count = 0
    fail_count = 0

    for question, expected_keywords in questions_and_expected_keywords:
        response = ask_bot(question)
        response_text = response.get("response", "").lower()
        
        matches = [fuzz.partial_ratio(keyword.lower(), response_text) for keyword in expected_keywords]
        if all(match >= threshold for match in matches):
            print(f"Question: {question}\nResponse: {response_text}\nStatus: PASS\n")
            pass_count += 1
        else:
            print(f"Question: {question}\nResponse: {response_text}\nStatus: FAIL\n")
            fail_count += 1

    print(f"Total PASS: {pass_count}")
    print(f"Total FAIL: {fail_count}")

# Fonction pour envoyer une question au bot
def ask_bot(question):
    params = {"query": question}
    response = requests.get(url, params=params)
    return response.json()

# Exécuter les tests
if __name__ == "__main__":
    test_bot_responses_fuzzy()
    #test_bot_responses()
