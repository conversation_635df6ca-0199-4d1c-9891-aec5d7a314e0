import streamlit as st
from PyPDF2 import PdfReader
from langchain.text_splitter import CharacterTextSplitter
from langchain_groq import ChatGroq
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from rank_bm25 import BM25Okapi
import numpy as np
import uuid
import json
import os
from datetime import datetime, timedelta
import pickle


MAX_TOTAL_SIZE_PER_USER = 10 * 1024 * 1024  # 10MB 
USERS_DATA_DIR = "users_data"
SESSION_TIMEOUT_DAYS = 2  


llm = ChatGroq(
    base_url="https://api.groq.com",
    model="meta-llama/llama-4-maverick-17b-128e-instruct",
    api_key="********************************************************",
)

# gestion des données utilisateur 
def ensure_users_directory():
    if not os.path.exists(USERS_DATA_DIR):
        os.makedirs(USERS_DATA_DIR)

def get_user_data_path(user_id):
    ensure_users_directory()
    user_dir = os.path.join(USERS_DATA_DIR, str(user_id))
    os.makedirs(user_dir, exist_ok=True)
    return os.path.join(user_dir, f"user_{user_id}.json")

def get_user_chunks_path(user_id):
    ensure_users_directory()
    user_dir = os.path.join(USERS_DATA_DIR, str(user_id))
    os.makedirs(user_dir, exist_ok=True)
    return os.path.join(user_dir, f"chunks_{user_id}.pkl")

def load_user_data(user_id):
    file_path = get_user_data_path(user_id)
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                last_activity = datetime.fromisoformat(data.get('last_activity', datetime.now().isoformat()))
                if datetime.now() - last_activity > timedelta(days=SESSION_TIMEOUT_DAYS):
                    # Session expirée, supprimer les données
                    cleanup_user_data(user_id)
                    return create_default_user_data()
                return data
        except (json.JSONDecodeError, KeyError, ValueError):
            return create_default_user_data()
    return create_default_user_data()

def save_user_data(user_id, data):
    data['last_activity'] = datetime.now().isoformat()
    file_path = get_user_data_path(user_id)
    with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

def load_user_chunks(user_id):
    file_path = get_user_chunks_path(user_id)
    if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                return pickle.load(f)
    return []

def save_user_chunks(user_id, chunks):
    file_path = get_user_chunks_path(user_id)
    with open(file_path, 'wb') as f:
        pickle.dump(chunks, f)

def cleanup_user_data(user_id):
    files_to_remove = [get_user_data_path(user_id), get_user_chunks_path(user_id)]
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)

def create_default_user_data():
    return {
        'history': [],
        'total_size': 0,
        'uploaded_files': [],
        'last_activity': datetime.now().isoformat()
    }




if "user_id" not in st.session_state:
    st.session_state.user_id = str(uuid.uuid4())

if "user_data" not in st.session_state:
    st.session_state.user_data = load_user_data(st.session_state.user_id)

if "pdf_chunks" not in st.session_state:
    st.session_state.pdf_chunks = load_user_chunks(st.session_state.user_id)


st.title("RAG Sans Embedding")


def extract_pdf_chunks(pdf_file, chunk_size=1000, chunk_overlap=200):
    reader = PdfReader(pdf_file)
    text = "".join([page.extract_text() or "" for page in reader.pages])
    print(f"Texte extrait : {len(text)} caractères")
    splitter = CharacterTextSplitter(separator=" ", chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    chunks = splitter.split_text(text)
    print(f"Nombre de chunks générés : {len(chunks)}")
    return chunks

def keyword_filter(query, chunks, min_match=1):
    query_words = set(query.lower().split())
    return [c for c in chunks if sum(w in c.lower() for w in query_words) >= min_match]

def get_top_chunks(query, chunks, top_k=3, max_chunk_len=400):
    filtered = keyword_filter(query, chunks)
    if not filtered:
        filtered = chunks 
    vectorizer = TfidfVectorizer().fit_transform([query] + filtered)
    cosine_sim = (vectorizer * vectorizer.T).toarray()
    scores = cosine_sim[0][1:]  
    top_indices = np.argsort(scores)[::-1][:top_k]
    selected = [filtered[i][:max_chunk_len] for i in top_indices]
    for msg in selected:
        print(msg)
        print("\n")
    return selected 

def build_prompt(user_question, context):
    history_context = ""
    if st.session_state.user_data['history']:
        recent_history = st.session_state.user_data['history'][-5:]  
        history_context = f"Historique récent: {recent_history}\n"
    
    return f"""
Tu es un assistant IA. Voici l'historique et le texte du PDF.
{history_context}
Contexte PDF:
{context}

Question: {user_question}
Réponds uniquement avec ce contexte disponible.
tu peut Ajouter des fleche ou icons pour clarifier les chose a le utilisateur
"""   

def display_history():   
    st.markdown('<div class="chat-box">', unsafe_allow_html=True)
    for msg in st.session_state.user_data['history']:
        if msg["role"] == "user":
            st.markdown(f'<div class="user-msg"><div class="sender">Vous :</div>{msg["content"]}</div>', unsafe_allow_html=True)
        else:
            st.markdown(f'<div class="bot-msg"><div class="sender">Bot :</div>{msg["content"]}</div>', unsafe_allow_html=True)
    st.markdown('</div>', unsafe_allow_html=True)


st.markdown("""
<style>
.chat-box { display: flex; flex-direction: column; margin-top: 10px; }
.user-msg {
    align-self: flex-end;
    background: #DCF8C6;
    color: #000;
    padding: 10px 15px;
    border-radius: 15px 15px 0 15px;
    margin: 5px 0;
    max-width: 70%;
}
.bot-msg {
    align-self: flex-start;
    background: #E9E9EB;
    color: #000;
    padding: 10px 15px;
    border-radius: 15px 15px 15px 0;
    margin: 5px 0;
    max-width: 70%;
}
.sender { font-size: 12px; font-weight: bold; margin-bottom: 2px; }
</style>
""", unsafe_allow_html=True)


st.subheader("Upload de PDF")
pdf = st.file_uploader("Upload ton PDF", type=["pdf"])

if pdf:
    file_size = pdf.size
    total_space = MAX_TOTAL_SIZE_PER_USER - st.session_state.user_data['total_size']
    
    if file_size > total_space:
        st.error(f"Espace Limitee ! ")
    else:
        
        file_hash = hash(pdf.read())
        pdf.seek(0)  
        
        if file_hash not in [f['hash'] for f in st.session_state.user_data['uploaded_files']]:
                chunks = extract_pdf_chunks(pdf)
                st.session_state.pdf_chunks.extend(chunks)
                
                
                st.session_state.user_data['total_size'] += file_size
                st.session_state.user_data['uploaded_files'].append({
                    'name': pdf.name,
                    'size': file_size,
                    'hash': file_hash,
                    'upload_date': datetime.now().isoformat()
                })
                
              
                save_user_data(st.session_state.user_id, st.session_state.user_data)
                save_user_chunks(st.session_state.user_id, st.session_state.pdf_chunks)
                
                st.success(f"PDF '{pdf.name}' chargé avec succès ! ({len(chunks)} chunks créés)")
                st.rerun()
            

if st.session_state.user_data['uploaded_files']:
    st.subheader("Fichiers telecharger")
    for i, file_info in enumerate(st.session_state.user_data['uploaded_files']):
        col1, col2, col3 = st.columns([3, 1, 1])
        with col1:
            st.text(f"📄 {file_info['name']}")
        with col2:
            st.text(f"{file_info['size']/(1024*1024):.2f} MB")
        with col3:
            upload_date = datetime.fromisoformat(file_info['upload_date'])
            st.text(upload_date.strftime("%d/%m/%Y"))
            
st.subheader("Chat")


if st.session_state.user_data['history']:
    display_history()


user_q = st.text_input("Pose ta question sur les PDFs uploadés:", key="user_question")

if st.button("Envoyer", type="primary") and user_q:
    if not st.session_state.pdf_chunks:
        st.warning("Veuillez d'abord uploader un PDF avant de poser une question.")
    else:
            context_chunks = get_top_chunks(user_q, st.session_state.pdf_chunks, top_k=3, max_chunk_len=1000)
            context = "\n".join(context_chunks)
            
            prompt = build_prompt(user_q, context)
            response = llm.invoke(prompt)
                
               
            st.session_state.user_data['history'].append({"role": "user", "content": user_q})
            st.session_state.user_data['history'].append({"role": "assistant", "content": response.content})
                
             
            save_user_data(st.session_state.user_id, st.session_state.user_data)
              
            st.rerun()

