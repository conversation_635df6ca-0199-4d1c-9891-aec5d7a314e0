from fastapi import APIRouter, Depends, Query, HTTPException
from src.dev.models.documentation_model import Documentation
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
from src.dev.schemas.client_schema import CreateClientSchema
from src.dev.models.enums.user_type import UserType
from src.dev.utils.auth import <PERSON><PERSON>he<PERSON>
from src.dev.services.auth.auth import AuthDataManager, AuthService, get_current_user
from src.dev.schemas.auth import UserSchema
from src.dev.config.config import Settings,get_settings
from sqlalchemy.orm import Session, joinedload
from src.dev.utils.dependencies import get_db
from pydantic import BaseModel
from datetime import datetime
from typing import Optional


from src.dev.models.enums.user_type import UserType
from src.dev.utils.auth import RoleChecker
from src.dev.services.auth.auth import get_current_user
from src.dev.schemas.auth import UserSchema

from src.dev.models.question_response_model import QuestionResponseLog
from src.dev.models.bot_model import Bo<PERSON>, BotStatus
from src.dev.models.user_model import Admin, Client, User
from datetime import datetime

base_router = APIRouter(
    prefix="/api/v1",
    tags=["api_v1"]
)

@base_router.get("/")
async def welcom(app_settings:Settings = Depends(get_settings)):
    app_name = app_settings.APP_NAME
    app_version = app_settings.APP_VERSION
    return {
        "app_name":app_name,
        "app_version":app_version
    }

@base_router.get("/question-responses")
def read_question_responses(
    page: Optional[int] = Query(1, ge=1),
    limit: Optional[int] = Query(10, le=100),
    feedback: Optional[int] = Query(None),
    db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ):
    query = db.query(QuestionResponseLog).options(joinedload(QuestionResponseLog.user), joinedload(QuestionResponseLog.bot))

    if feedback is not None:
        query = query.filter(QuestionResponseLog.feedback == feedback)

    total_counts = query.count()
    results = query.offset((page - 1) * limit).limit(limit).all()

    return {
        "results": results,
        "page": page,
        "limit": limit,
        "total_count": total_counts
    }

@base_router.get("/bot/{bot_id}/question-responses")
def read_question_responses(bot_id: int, db: Session = Depends(get_db)):
    query = db.query(QuestionResponseLog).options(joinedload(QuestionResponseLog.user), joinedload(QuestionResponseLog.bot))
    if bot_id is not None:
        results = query.filter(QuestionResponseLog.idBot == bot_id).all()
    else:
        results = query.all()
    return results

class UpdateResponse(BaseModel):
    response: str

class DeleteEntry(BaseModel):
    response: str

@base_router.put("/update-response/{idLog}", response_model=UpdateResponse)
def update_response(idLog: int, update_response: UpdateResponse, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ):
    db_question_response = db.query(QuestionResponseLog).filter(QuestionResponseLog.idLog == idLog).first()
    if db_question_response is None:
        raise HTTPException(status_code=404, detail="Question response log not found")
    db_question_response.response = update_response.response
    db_question_response.feedback = 2
    db.commit()
    db.refresh(db_question_response)
    return db_question_response

@base_router.delete("/delete-response/{idLog}", response_model=DeleteEntry)
def delete_response(idLog: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ):
    db_question_response = db.query(QuestionResponseLog).filter(QuestionResponseLog.idLog == idLog).first()
    if db_question_response is None:
        raise HTTPException(status_code=404, detail="Question response log not found")
    db.delete(db_question_response)
    db.commit()
    return db_question_response

class FeedbackCreate(BaseModel):
    idBot: int
    question: str
    response: str
    feedbackText: str
    feedback: int


@base_router.post("/add-feedback", response_model=FeedbackCreate)
def create_feedback(feedback: FeedbackCreate, db: Session = Depends(get_db),user: UserSchema = Depends(get_current_user)):
    feedback_data = QuestionResponseLog(
        id_bot=feedback.idBot,
        user_id=user.user_id,
        question=feedback.question,
        response=feedback.response,
        feedback=feedback.feedback,  # Automatically set feedback to 2
        feedback_text=feedback.feedbackText,
    )
    db.add(feedback_data)
    db.commit()
    db.refresh(feedback_data)
    return feedback_data

@base_router.get("/bots" )
def read_bots(user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])), db: Session = Depends(get_db),):
    bots = db.query(Bot).all()
    if bots is None:
        raise HTTPException(status_code=404, detail="Question response log not found")
    
    if (user.type == UserType.admin):
        return bots
    else:
        return AuthDataManager(db).get_user_bots(user_id=user.user_id)


@base_router.get("/bots/{bot_id}")
def get_bot_details(bot_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    bot = db.query(Bot).filter(Bot.idBot == bot_id).first()
    if bot:
        return bot
    raise HTTPException(status_code=404, detail="Bot not found")

class BotUpdateSchema(BaseModel):
    botName: str 
    applicationName: str 
    currentInputToken: int 
    currentOutputToken: int 
    isLimitReached: int 
    status: BotStatus


@base_router.put("/bots/{bot_id}")
def update_bot(
    bot_id: int,
    bot_data: BotUpdateSchema,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    bot = db.query(Bot).filter(Bot.idBot == bot_id).first()
    if bot:
        for key, value in bot_data.model_dump(exclude_unset=True).items():
            setattr(bot, key, value)
        db.commit()
        db.refresh(bot)
        return bot 
    raise HTTPException(status_code=404, detail="Bot not found")


@base_router.delete("/bots/{bot_id}")
def delete_bot(bot_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    bot = db.query(Bot).filter(Bot.idBot == bot_id).first()
    if bot:
        # Delete all dependent records in BotIncludeDocumentation
        db.query(BotIncludeDocumentation).filter(BotIncludeDocumentation.idBot == bot_id).delete()
        db.delete(bot)
        db.commit()
        return {"message": "Bot and related documents deleted successfully"}
    raise HTTPException(status_code=404, detail="Bot not found")


class BotCreateSchema(BaseModel):
    idSubscription: int
    botName:str 
    applicationName: str
    currentInputToken: int
    currentOutputToken: int
    isLimitReached: int

@base_router.post("/bots", status_code=201)
def create_bot(bot_data: BotCreateSchema, db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    new_bot = Bot(
        idSubscription=bot_data.idSubscription,
        botName=bot_data.botName,
        applicationName=bot_data.applicationName,
        currentInputToken=bot_data.currentInputToken,
        currentOutputToken=bot_data.currentOutputToken,
        isLimitReached=bot_data.isLimitReached,
    )
    db.add(new_bot)
    db.commit()
    db.refresh(new_bot)
    return new_bot
class BotDuplicateSchema(BaseModel):
    new_id: int

@base_router.put("/bots/{bot_id}/duplicate")
def duplicate_bot(bot_id: int, bot_data: BotDuplicateSchema, db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    # Fetch the bot to be duplicated
    bot_to_duplicate = db.query(Bot).filter(Bot.idBot == bot_id).first()
    if not bot_to_duplicate:
        raise HTTPException(status_code=404, detail="Bot not found")

    # Check if the new ID already exists
    existing_bot = db.query(Bot).filter(Bot.idBot == bot_data.new_id).first()
    if existing_bot:
        raise HTTPException(status_code=400, detail="New Bot ID already exists")

    # Create the new bot with the same details but a new ID
    new_bot = Bot(
        idBot=bot_data.new_id,
        idSubscription=bot_to_duplicate.idSubscription,
        botName=bot_to_duplicate.botName,
        applicationName=bot_to_duplicate.applicationName,
        currentInputToken=bot_to_duplicate.currentInputToken,
        currentOutputToken=bot_to_duplicate.currentOutputToken,
        isLimitReached=bot_to_duplicate.isLimitReached,
      
    )

    # Add the new bot to the session and commit the transaction
    db.add(new_bot)
    db.commit()
    db.refresh(new_bot)

    return {"message": "Bot duplicated successfully", "bot": new_bot}



# CRUD for Clients
@base_router.get("/clients" )
def read_clients(db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin])
        )
    ):
    bots = db.query(Client).all()
    if bots is None:
        raise HTTPException(status_code=404, detail="Question response log not found")
    
    return bots

#get client by id
@base_router.get("/clients/{client_id}")
def get_client_details(client_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin])
        )
    ):
    client = db.query(Client).filter(Client.userId == client_id).first()
    if client:
        return client
    raise HTTPException(status_code=404, detail="Client not found")

class ClientUpdateSchema(BaseModel):
    userName: str
    email: str
    companyName: str
    password: str

#Update client info
@base_router.put("/clients/{client_id}")
def update_client(client_id: int, client_data: ClientUpdateSchema, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin])
        )
                  ):
    client = db.query(Client).filter(Client.userId == client_id).first()
    if client:
        for key, value in client_data.dict().items():
            setattr(client, key, value)
        db.commit()
        return {"message": "Client updated successfully"}
    raise HTTPException(status_code=404, detail="Client not found")

#delete client
@base_router.delete("/clients/{client_id}")
def delete_client(client_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin])
        )
                  ):
    client = db.query(Client).filter(Client.userId == client_id).first()
    if client:
        db.delete(client)
        db.commit()
        return {"message": "Client deleted successfully"}
    raise HTTPException(status_code=404, detail="Client not found")


@base_router.post("/clients", status_code=201)
def create_client(client_data: CreateClientSchema, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin])
        )  
    ):
    admin = db.query(Admin).filter(Admin.userId == client_data.adminUserId).first()
    if not admin:
        raise HTTPException(status_code=404, detail="Admin not found")

    return AuthService(db).create_account(client_data)


# Endpoints for Documentation
@base_router.get("/documentation")
def get_all_documentation(db: Session = Depends(get_db)):
    all_documentation = db.query(Documentation).all()
    if not all_documentation:
        raise HTTPException(status_code=404, detail="No documentation found")
    return all_documentation

@base_router.get("/bots/{bot_id}/documentation")
def get_bot_documentation(bot_id: int, db: Session = Depends(get_db)):
    bot = db.query(Bot).filter(Bot.idBot == bot_id).first()
    if not bot:
        raise HTTPException(status_code=404, detail="Bot not found")
    
    bot_documentation = db.query(Documentation).join(BotIncludeDocumentation).filter(BotIncludeDocumentation.idBot == bot_id).all()
    return bot_documentation