
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String
from src.dev.utils.database import Base
#from sqlalchemy.ext.declarative import declarative_base
from pgvector.sqlalchemy import Vector
# from sqlalchemy.ext.declarative import declarative_base
from pgvector.sqlalchemy import Vector
from sqlalchemy import Column, Integer, String
from src.dev.utils.database import Base


#Base = declarative_base()

class EmbeddingData(Base):
    __tablename__ = "embeddings_data"

    chunkId = Column(Integer, primary_key=True, index=True)
    botId = Column(Integer, nullable=False)
    docId = Column(Integer, nullable=False)
    #chunkText = Column(String, unique=True, nullable=False)
    chunkText = Column(String, nullable=False)
    embeddings = Column(Vector(768), nullable=False)
    tokenized_tokens = Column(JSON)
    docUrl = Column(String, nullable=False)
    pageNumber = Column(Integer, nullable=False)