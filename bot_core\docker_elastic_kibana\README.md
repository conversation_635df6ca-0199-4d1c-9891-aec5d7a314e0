# ⚙️ Stack Elasticsearch + Kibana avec Docker

Déploiement d’une stack Elasticsearch + Kibana sur Docker, sécurisée avec authentification, et exposée sur des ports personnalisés.

---

## 📁 <PERSON>chi<PERSON> `docker-compose.yml`

```yaml
version: "3.7"

services:
  elasticsearch:
    container_name: elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.15.0
    volumes:
      - elasticdata:/usr/share/elasticsearch/data
    environment:
      - xpack.security.enabled=true
      - xpack.security.enrollment.enabled=true
      - bootstrap.memory_lock=true
      - discovery.type=single-node
      - network.host=0.0.0.0
      - http.port=16405
      - ELASTIC_PASSWORD=admin12024
    ports:
      - 16405:16405
    networks:
      - elastic
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  kibana:
    image: docker.elastic.co/kibana/kibana:8.15.0
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:16405
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=16407
      - ELASTICSEARCH_SERVICEACCOUNTTOKEN=__À_REMPLACER_PAR_VOTRE_TOKEN__
      - xpack.encryptedSavedObjects.encryptionKey=__À_REMPLACER_PAR_VOTRE_KEY_32CHARS__
    ports:
      - 16407:16407
    networks:
      - elastic
    depends_on:
      - elasticsearch

networks:
  elastic:

volumes:
  elasticdata:
```

---

## 🚀 Étapes de déploiement

### 1. Nettoyer (optionnel mais recommandé)

```bash
docker compose down -v
```

---

### 2. Lancer uniquement Elasticsearch pour initialisation

```bash
docker compose up elasticsearch
```

Attendez que les logs indiquent que la sécurité est activée (`xpack.security.enabled`).

---

### 3. Générer un token d’accès pour Kibana

Dans un autre terminal :

```bash
docker exec -it elasticsearch bin/elasticsearch-service-tokens create elastic/kibana kibana-service-token
```

Copiez le token renvoyé (il commence par `AAEAAW...`) et remplacez la valeur dans la variable :

```env
ELASTICSEARCH_SERVICEACCOUNTTOKEN=AAEAAW...
```

---

### 4. Lancer Kibana

```bash
docker compose up -d kibana
```

---

## 🌍 Accès aux interfaces

| Interface     | URL                  |
|---------------|----------------------|
| Elasticsearch | http://localhost:16405 |
| Kibana        | http://localhost:16407 |

---

## 🔐 Authentification & Tests

### ✅ Connexion avec l’utilisateur `elastic`

```bash
curl -u elastic:admin12024 http://localhost:16405/_security/user?pretty
```

### 📂 Lister les index

```bash
curl -u elastic:admin12024 http://localhost:16405/_cat/indices?v
```

---

## ⚠️ Problèmes fréquents

### ❌ `index_not_found_exception: no such index [.security]`

➡️ Elasticsearch n’a pas encore généré l’index `.security`. Attendez 30 à 60 secondes.

---

### ❌ `username "elastic" is forbidden`

➡️ Kibana ne peut pas utiliser le compte `elastic`. Utilisez uniquement un token de service comme indiqué plus haut.

---

## 📜 Script d'automatisation

Voici un script `setup.sh` automatisé pour lancer Elasticsearch + Kibana, générer le token de service, et vous afficher les URLs à consulter :

Placez le script [`setup_docker_elastic_kibana.sh`](setup_docker_elastic_kibana.sh) à la racine de votre projet (même dossier que [`docker-compose.yml`](docker-compose.yml)) :

```bash
chmod +x setup.sh
./setup.sh
```