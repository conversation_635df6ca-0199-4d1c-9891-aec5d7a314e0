from src.dev.services.rag.rag_processor_factory import get_rag_processor
from src.dev.services.auth.auth import get_current_user
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
import logging
import os
import json

import aiofiles
from mimetypes import guess_type

import requests
from fastapi import APIRouter, Depends, UploadFile, status, HTTPException
from fastapi.responses import JSONResponse, FileResponse
from llama_index.core import PromptTemplate
from llama_index.llms.ollama import Ollama
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import RoleChecker
from src.dev.services.auth.auth import get_current_user
from src.dev.api import ClientController
from src.dev.config.config import Settings, get_settings
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.bot_model import Bot
from src.dev.models.documentation_model import Documentation
from src.dev.models.enums.DocumentTypeEnum import DocumentTypeEnum
from src.dev.models.enums.ResponseEnum import UploadFileResponses
from src.dev.utils.auth import RoleChecker
from src.dev.utils.dependencies import get_db
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from fastapi.responses import JSONResponse
from src.dev.utils.ask_llm_utils import core_ask_logic, get_or_create_user_temp_data, update_user_activity, get_user_chunks, save_user_chunks, get_top_chunks_temp, build_temp_rag_prompt, enrich_context_with_temp_documents
from src.dev.services.rag.vectorizer import vectorize_and_store_document
from PyPDF2 import PdfReader
from langchain.text_splitter import CharacterTextSplitter
import hashlib
import json
import os
from datetime import datetime


logger = logging.getLogger("uvicorn.error")

client_router = APIRouter(
    prefix="/api/client",
    tags=["api_client"]
)

def build_relative_path(client_id: int, bot_id: int, filename: str) -> str:
    """
    Construit un chemin relatif standardisé pour les documents
    Format: /docs/{client_id}/{bot_id}/{filename}
    """
    return f"/docs/{client_id}/{bot_id}/{filename}"

def get_absolute_path_from_relative(relative_path: str, app_settings: Settings) -> str:
    """
    Convertit un chemin relatif en chemin absolu
    """
    # Si le chemin commence par /docs/, on le garde tel quel
    if relative_path.startswith("/docs/"):
        path_without_leading_slash = relative_path[1:]  # Enlever seulement le "/" initial
    elif relative_path.startswith("/"):
        path_without_leading_slash = relative_path[1:]  # Enlever le "/" initial
    else:
        path_without_leading_slash = relative_path

    # Construire le chemin absolu
    absolute_path = os.path.join(app_settings.ROOT_STORAGE_PATH, path_without_leading_slash)

    # Normaliser le chemin pour gérer les différences entre Windows et Linux
    absolute_path = os.path.normpath(absolute_path)

    # Log pour debugging
    logging.info(f"Converting relative path: {relative_path}")
    logging.info(f"Path without leading slash: {path_without_leading_slash}")
    logging.info(f"ROOT_STORAGE_PATH: {app_settings.ROOT_STORAGE_PATH}")
    logging.info(f"Final absolute path: {absolute_path}")
    logging.info(f"File exists: {os.path.exists(absolute_path)}")

    return absolute_path

# this is not used upload 
@client_router.post("/uploadold/{client_id}/bot/{bot_id}", description="""
** WS not used to delete **

#### What happens internally
1. Insert document in documentation 
""")
async def upload_file(client_id: int, bot_id: int, file: UploadFile, app_settings: Settings = Depends(get_settings), db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ):  
    #validate the file properties
    is_valid, result_signal = ClientController().validate_uploaded_file(file=file)
    if not is_valid:
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "signal": result_signal
            }
        )
    
    #create path for file storage
    folder_path = ClientController().get_client_folder_path(client_id=client_id, bot_id=bot_id)
    if not file.filename:
        raise ValueError("File name is missing while uploading file")

    file_path = os.path.join(folder_path, file.filename)
    
    #save file
    try:
        async with aiofiles.open(file_path, "wb") as f:
            while chunk := await file.read(app_settings.FILE_DEFAULT_CHUNK_SIZE):
                await f.write(chunk)
    except Exception as e:
        logger.error(f"Error while uploading file : {e}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "signal": UploadFileResponses.FILE_UPLOAD_FAILED
            }
        )
    
    # Construire le chemin relatif standardisé
    relative_path = build_relative_path(client_id, bot_id, file.filename)

    # add file path to DBs - STOCKER LE CHEMIN RELATIF
    doc = Documentation(
        title=file.filename,
        url=relative_path,  # Chemin relatif standardisé
        type=DocumentTypeEnum.TYPE_DOC_FILE.value, 
        embedded=False
        #, lastUpdated=datetime
        )
    db.add(doc)
    db.commit()
    db.refresh(doc)

    return JSONResponse(
            content={
                "signal": UploadFileResponses.FILE_UPLOAD_SUCCESS.value,
                "document": {
                    "id": doc.idDocumentation,
                    "url": doc.url,  # Chemin relatif directement utilisable
                    "title": doc.title
                }
            }
        )

@client_router.post("/upload/{client_id}/bot/{bot_id}", description="""
WS à utiliser pour uploader les documents.

#### What happens internally
1. Insert document in documentation
2. Insert in table BotIncludeDocumentation
""")
async def upload_document(
    client_id: int,
    bot_id: int,
    file: UploadFile,
    app_settings: Settings = Depends(get_settings),
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    # Override client_id with the current user's ID TODO remove the param from the url 
    client_id = user.user_id
    logging.info(f"Received request to upload document for client_id: {client_id}, bot_id: {bot_id}")
    logging.info(f"The ID of the current user is: {user.user_id}")

    # Validate the file properties
    is_valid, result_signal = ClientController().validate_uploaded_file(file=file)
    if not is_valid:
        logging.error(f"File validation failed: {result_signal}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"signal": result_signal}
        )

    # Create path for file storage
    folder_path = ClientController().get_client_folder_path(client_id=client_id, bot_id=bot_id)
    os.makedirs(folder_path, exist_ok=True)  # Ensure the folder exists

    if not file.filename:
        raise ValueError("File name is missing while uploading file")
    
    file_path = os.path.join(folder_path, file.filename)

    logging.info('###')
    logging.info(f'folder_path: {folder_path}')
    logging.info(f'file_path: {file_path}')

    # Save file
    try:
        async with aiofiles.open(file_path, "wb") as f:
            while chunk := await file.read(app_settings.FILE_DEFAULT_CHUNK_SIZE):
                await f.write(chunk)
    except Exception as e:
        logging.error(f"Error while uploading file: {e}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"signal": UploadFileResponses.FILE_UPLOAD_FAILED}
        )

    # Detect file type dynamically
    mime_type, _ = guess_type(file.filename)
    if file.filename.lower().endswith('.csv'):
        mime_type = 'text/csv'  # Force le type MIME pour les fichiers CSV
    if mime_type is None:
        mime_type = "application/octet-stream"  # Default fallback

    logging.info(f"Detected MIME type: {mime_type}")

    # Map MIME type to DocumentTypeEnum
    type_mapping = {
        "application/pdf": DocumentTypeEnum.TYPE_DOC_PDF.value,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": DocumentTypeEnum.TYPE_DOC_EXCEL.value,
        "text/plain": DocumentTypeEnum.TYPE_DOC_TXT.value,
        "text/csv": DocumentTypeEnum.TYPE_DOC_CSV.value,
        "text/html": DocumentTypeEnum.TYPE_DOC_HTML_PAGE.value,
    }
    document_type = type_mapping.get(mime_type, DocumentTypeEnum.TYPE_DOC_FILE.value)  # Default to "file" if unknown
    logging.info(f"Detected MIME type: {mime_type}")
    logging.info(f"Mapped document type: {document_type}")
    
    # Construire le chemin relatif standardisé
    relative_path = build_relative_path(client_id, bot_id, file.filename)

    # Add file path to DB avec le chemin relatif
    doc = Documentation(
        title=file.filename,
        url=relative_path,  # Chemin relatif standardisé
        type=document_type,
        embedded=False
    )
    db.add(doc)
    db.commit()
    db.refresh(doc)

    bot_include_doc = BotIncludeDocumentation(
        idBot=bot_id,
        idDocumentation=doc.idDocumentation
    )
    db.add(bot_include_doc)
    db.commit()
    db.refresh(bot_include_doc)

    return JSONResponse(
        content={
            "signal": UploadFileResponses.FILE_UPLOAD_SUCCESS.value,
            "document": {
                "idDocumentation": doc.idDocumentation,
                "type": doc.type,
                "url": doc.url,  # Chemin relatif
                "title": doc.title,
                "embedded": doc.embedded,
            }
        }
    )
   

# code Ajouter 
@client_router.post("/upload-temp/{conversation_id}/bot/{bot_id}", description="""
WS à utiliser pour uploader des documents temporaires.

#### What happens internally
1. Upload le document dans un dossier temporaire
2. Insère l'entrée dans la table ConversationTempDocument
""")
async def upload_temp_document(
    conversation_id: str,
    bot_id: int,
    file: UploadFile,
    app_settings: Settings = Depends(get_settings),
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    # Validate the file properties
    is_valid, result_signal = ClientController().validate_uploaded_file(file=file)
    if not is_valid:
        logging.error(f"File validation failed: {result_signal}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"signal": result_signal}
        )

    # Create path for temporary file storage
    temp_folder_path = os.path.join(app_settings.ROOT_STORAGE_PATH, "temp", conversation_id)
    os.makedirs(temp_folder_path, exist_ok=True)  # Ensure the folder exists

    if not file.filename:
        raise ValueError("File name is missing while uploading file")
    
    file_path = os.path.join(temp_folder_path, file.filename)

    logging.info('###')
    logging.info(f'temp_folder_path: {temp_folder_path}')
    logging.info(f'file_path: {file_path}')

    # Save file
    try:
        async with aiofiles.open(file_path, "wb") as f:
            while chunk := await file.read(app_settings.FILE_DEFAULT_CHUNK_SIZE):
                await f.write(chunk)
    except Exception as e:
        logging.error(f"Error while uploading temporary file: {e}")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={"signal": UploadFileResponses.FILE_UPLOAD_FAILED}
        )

    # Add file path to DB
    from src.dev.models.conversation import ConversationTempDocument
    from datetime import datetime, timedelta
    
    temp_doc = ConversationTempDocument(
        conversationId=conversation_id,
        filename=file.filename,
        filepath=file_path,
        uploadedAt=datetime.utcnow(),
        expiresAt=datetime.utcnow() + timedelta(hours=1),
        uploadedBy=user.user_id
    )
    db.add(temp_doc)
    db.commit()
    db.refresh(temp_doc)

    return JSONResponse(
        content={
            "signal": UploadFileResponses.FILE_UPLOAD_SUCCESS.value,
            "document": {
                "id": temp_doc.id,
                "filename": temp_doc.filename,
                "uploadedAt": temp_doc.uploadedAt.isoformat(),
                "expiresAt": temp_doc.expiresAt.isoformat()
            }
        }
    )   
    
    
    
    
    
    
    
    

@client_router.put("/files/{document_id}")
async def update_document_file(
    document_id: int,
    file: UploadFile,  # Nouveau fichier à uploader
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])),
    app_settings: Settings = Depends(get_settings)
):
    # Récupérer le document à partir de la base de données
    doc = db.query(Documentation).filter(Documentation.idDocumentation == document_id).first()
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found.")

    # Valider le fichier
    is_valid, result_signal = ClientController().validate_uploaded_file(file=file)
    if not is_valid:
        logging.error(f"File validation failed: {result_signal}")
        raise HTTPException(status_code=400, detail=result_signal)

    # Récupérer le bot_id depuis la table BotIncludeDocumentation
    bot_include_doc = db.query(BotIncludeDocumentation).filter(
        BotIncludeDocumentation.idDocumentation == document_id
    ).first()
    if not bot_include_doc:
        raise HTTPException(status_code=404, detail="Bot association not found.")

    bot_id = bot_include_doc.idBot
    client_id = user.user_id

    # Convertir l'ancien chemin relatif en chemin absolu et supprimer l'ancien fichier
    old_relative_path = doc.url
    old_file_path = get_absolute_path_from_relative(old_relative_path, app_settings)
    if os.path.exists(old_file_path):
        try:
            os.remove(old_file_path)
            logging.info(f"Old file deleted: {old_file_path}")
        except Exception as e:
            logging.error(f"Error while deleting old file: {e}")
            raise HTTPException(status_code=500, detail="Failed to delete old file.")

    # Créer le chemin pour le nouveau fichier
    folder_path = ClientController().get_client_folder_path(client_id=client_id, bot_id=bot_id)
    os.makedirs(folder_path, exist_ok=True)  # Assurez-vous que le dossier existe
    new_file_path = os.path.join(folder_path, file.filename)

    # Sauvegarder le nouveau fichier
    try:
        async with aiofiles.open(new_file_path, "wb") as f:
            while chunk := await file.read(app_settings.FILE_DEFAULT_CHUNK_SIZE):
                await f.write(chunk)
    except Exception as e:
        logging.error(f"Error while uploading new file: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload new file.")

    # Construire le nouveau chemin relatif
    new_relative_path = build_relative_path(client_id, bot_id, file.filename)

    # Mettre à jour les informations du document dans la base de données
    doc.title = file.filename
    doc.url = new_relative_path  # Chemin relatif standardisé
    doc.embedded = False
    db.commit()
    db.refresh(doc)

    return {
        "signal": "Document file updated successfully",
        "document": {
            "id": doc.idDocumentation,
            "title": doc.title,
            "url": doc.url,  # Chemin relatif
            "embedded": doc.embedded,
            "type": doc.type
        }
    }

@client_router.delete("/files/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])),
    app_settings: Settings = Depends(get_settings)
):
    # Récupérer le document à partir de la base de données
    doc = db.query(Documentation).filter(Documentation.idDocumentation == document_id).first()
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found.")

    # Log du document récupéré
    logging.info(f"Document to delete: ID={doc.idDocumentation}, URL={doc.url}, Title={doc.title}")

    relative_path = doc.url

    # Vérifier si c'est un chemin absolu Windows/Linux (pas juste commençant par /)
    if os.path.isabs(relative_path) and not relative_path.startswith('/docs/'):
        # C'est un vrai chemin absolu (ancien format)
        file_path = relative_path
        logging.info(f"Using absolute path directly: {file_path}")
    else:
        # C'est un chemin relatif ou commence par /docs/
        file_path = get_absolute_path_from_relative(relative_path, app_settings)

    # Supprimer le fichier physique du serveur
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            logging.info(f"File deleted: {file_path}")
        except Exception as e:
            logging.error(f"Error while deleting file: {e}")
            raise HTTPException(status_code=500, detail="Failed to delete file.")
    else:
        logging.warning(f"File not found on server: {file_path}")

    # Supprimer l'association avec le bot (si elle existe)
    bot_include_doc = db.query(BotIncludeDocumentation).filter(
        BotIncludeDocumentation.idDocumentation == document_id
    ).first()
    if bot_include_doc:
        db.delete(bot_include_doc)

    # Supprimer le document de la base de données
    db.delete(doc)
    db.commit()

    return {"signal": "Document deleted successfully"}


@client_router.get("/files/{document_id}")
async def download_document(
    document_id: int,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])),
    app_settings: Settings = Depends(get_settings)
):
    doc = db.query(Documentation).filter(Documentation.idDocumentation == document_id).first()
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found.")

    # Log du document récupéré
    logging.info(f"Document found: ID={doc.idDocumentation}, URL={doc.url}, Title={doc.title}")

    relative_path = doc.url

    # Vérifier si c'est un chemin absolu Windows/Linux (pas juste commençant par /)
    if os.path.isabs(relative_path) and not relative_path.startswith('/docs/'):
        # C'est un vrai chemin absolu (ancien format)
        file_path = relative_path
        logging.info(f"Using absolute path directly: {file_path}")
    else:
        # C'est un chemin relatif ou commence par /docs/
        file_path = get_absolute_path_from_relative(relative_path, app_settings)

    if os.path.exists(file_path):
        return FileResponse(path=file_path, filename=os.path.basename(file_path))
    else:
        logging.error(f"File not found at path: {file_path}")
        logging.error(f"Original relative path: {relative_path}")

        # Essayer d'autres chemins possibles pour debugging
        alternative_paths = [
            os.path.join(app_settings.ROOT_STORAGE_PATH, relative_path.lstrip('/')),
            os.path.join(app_settings.ROOT_STORAGE_PATH, relative_path.lstrip('/docs/')),
            relative_path
        ]

        for alt_path in alternative_paths:
            logging.info(f"Trying alternative path: {alt_path} - Exists: {os.path.exists(alt_path)}")

        raise HTTPException(status_code=404, detail="File not found.")


@client_router.post("/process/{client_id}/bot/{bot_id}")
async def vectorize(
    client_id: int,
    bot_id: int,
    document_id: int,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])),
    app_settings: Settings = Depends(get_settings)  # Add this dependency
):
    # Get document from database
    doc = db.query(Documentation).filter(Documentation.idDocumentation == document_id).first()
    if not doc:
        raise HTTPException(status_code=404, detail="Document not found")

    # Convert relative path to absolute path
    relative_path = doc.url

    # Check if it's already an absolute path (for backward compatibility)
    if os.path.isabs(relative_path) and not relative_path.startswith('/docs/'):
        # It's already an absolute path (old format)
        absolute_path = relative_path
    else:
        # Convert relative path to absolute path using the helper function
        absolute_path = get_absolute_path_from_relative(relative_path, app_settings)

    # Verify the file exists before processing
    if not os.path.exists(absolute_path):
        logging.error(f"File not found at path: {absolute_path}")
        raise HTTPException(status_code=404, detail=f"File not found at path: {relative_path}")

    # Log the paths for debugging
    logging.info(f"Processing document: ID={document_id}, Relative path={relative_path}, Absolute path={absolute_path}")

    # Temporarily update the document's URL to the absolute path for processing
    original_url = doc.url
    doc.url = absolute_path

    try:
        # Call vectorize_and_store_document
        result = vectorize_and_store_document(bot_id=bot_id, document_id=document_id, db=db)
        return result
    finally:
        # Restore the original relative path
        doc.url = original_url


@client_router.get("/ask/bot/{bot_id}")
async def askLLM(bot_id: int, query: str, db: Session = Depends(get_db), user: UserSchema = Depends(get_current_user)):
    client_id = user.user_id

    # Get bot information
    bot = db.query(Bot).filter_by(idBot=bot_id).first()
    if not bot:
        raise HTTPException(status_code=404, detail="Bot not found")

    # Get RAG processor
    processor = get_rag_processor()

    try:
        # Load chunks and tokenized corpus for hybrid retrieval
        chunks_data = processor.load_chunks_with_tokens(bot_id, db)
        chunks = chunks_data["chunks"]
        tokenized_corpus = chunks_data["tokenized_corpus"]

        if not chunks:
            return JSONResponse(
                content={
                    "msg": "Je ne sais pas ! j'apprends ... repose la question plus tard...",
                    "context": []
                }
            )

        # Perform hybrid retrieval
        retrieved_passages = processor.hybrid_retrieval(query, chunks, tokenized_corpus, top_k=5)

        # Rerank passages
        reranked_passages = processor.rerank_passages(query, retrieved_passages)

        # Prepare context
        context = "\n\n".join(reranked_passages)

        # Get documents for context (for response metadata)
        doc_results = (
            db.query(Documentation)
            .join(EmbeddingData, EmbeddingData.docId == Documentation.idDocumentation)
            .filter(EmbeddingData.botId == bot_id)
            .distinct()
            .limit(3)
            .all()
        )

        # Prepare context list with relative paths
        context_list = [
            {
                "id": doc.idDocumentation,
                "title": doc.title,
                "url": doc.url,  # Déjà un chemin relatif
                "type": doc.type
            }
            for doc in doc_results
        ]

        # Prepare prompt
        qa_prompt_tmpl_str = """
        Vous êtes un Assistant travaillant pour Technologie & Telecom.
        Vous serez chargé de répondre en français aux questions des clients concernant
        la manière dont ils peuvent interagir avec les applications de T&T.
        
        Sans dire bonjour répondez à la question suivante: {query_str} en vous basant uniquement sur le CONTEXTE fourni. 
        Réponds avec politesse les interactions humains basiques et appart cela si la réponse n'est pas trouvée dans le contexte, répondez "Je ne sais pas ! j'apprends ... repose la question plus tard...".
        CONTEXT :
        {CONTEXT}
        """

        prompt_tmpl = PromptTemplate(qa_prompt_tmpl_str)

        fmt_prompt = prompt_tmpl.format(
            query_str=query,
            CONTEXT=context
        )

        # Get response from LLM
        llm = Ollama(model="llama3.1:8b", request_timeout=120.0)
        response = llm.complete(fmt_prompt)

        logger.info(f"Response : {response.text}")

        return {
            "msg": response.text,
            "context": context_list
        }

    except Exception as e:
        logging.getLogger("uvicorn").exception(f"Error in askLLM: {str(e)}")
        return JSONResponse(
            content={
                "msg": "Je ne sais pas ! j'apprends ... repose la question plus tard...",
                "context": []
            }
        )


# ─────────────────────────────────────────────────────────────
# Pipeline RAG Temporaire - Routes
# ─────────────────────────────────────────────────────────────

@client_router.post("/temp-rag/upload")
async def upload_temp_pdf(
    file: UploadFile,
    user_id: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Upload d'un PDF pour le pipeline RAG temporaire.
    """
    MAX_TOTAL_SIZE_PER_USER = 10 * 1024 * 1024  # 10MB

    # Validation du fichier
    if not file.filename or not file.filename.endswith('.pdf'):
        return JSONResponse(
            status_code=400,
            content={"error": "Seuls les fichiers PDF sont acceptés"}
        )

    # Récupérer ou créer les données utilisateur
    user_data = get_or_create_user_temp_data(user_id, db)

    # Vérifier l'espace disponible
    file_size = 0
    content = await file.read()
    file_size = len(content)

    total_space = MAX_TOTAL_SIZE_PER_USER - user_data.total_size
    if file_size > total_space:
        return JSONResponse(
            status_code=400,
            content={"error": "Espace limité dépassé"}
        )

    # Calculer le hash du fichier
    file_hash = hashlib.md5(content).hexdigest()

    # Vérifier si le fichier n'est pas déjà uploadé
    uploaded_files = json.loads(user_data.uploaded_files or "[]")
    if any(f['hash'] == file_hash for f in uploaded_files):
        return JSONResponse(
            status_code=400,
            content={"error": "Ce fichier a déjà été uploadé"}
        )

    try:
        # Extraire le texte du PDF
        from io import BytesIO
        pdf_file = BytesIO(content)
        reader = PdfReader(pdf_file)
        text = "".join([page.extract_text() or "" for page in reader.pages])

        # Diviser en chunks
        splitter = CharacterTextSplitter(
            separator=" ",
            chunk_size=1000,
            chunk_overlap=200
        )
        chunks = splitter.split_text(text)

        # Sauvegarder les chunks
        save_user_chunks(user_id, chunks, file_hash, db)

        # Mettre à jour les informations utilisateur
        uploaded_files.append({
            'name': file.filename,
            'size': file_size,
            'hash': file_hash,
            'upload_date': datetime.utcnow().isoformat()
        })

        user_data.uploaded_files = json.dumps(uploaded_files)
        user_data.total_size += file_size
        update_user_activity(user_id, db)

        return JSONResponse(
            content={
                "message": f"PDF '{file.filename}' chargé avec succès",
                "chunks_created": len(chunks),
                "file_hash": file_hash
            }
        )

    except Exception as e:
        logger.error(f"Erreur lors du traitement du PDF: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Erreur lors du traitement du fichier"}
        )


@client_router.get("/temp-rag/ask")
async def ask_temp_rag(
    user_id: str,
    query: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Poser une question au pipeline RAG temporaire.
    """
    try:
        # Récupérer les chunks de l'utilisateur
        chunks = get_user_chunks(user_id, db)

        if not chunks:
            return JSONResponse(
                content={
                    "msg": "Veuillez d'abord uploader un PDF avant de poser une question.",
                    "context": []
                }
            )

        # Obtenir les meilleurs chunks pour la requête
        context_chunks = get_top_chunks_temp(query, chunks, top_k=3, max_chunk_len=1000)
        context = "\n".join(context_chunks)

        # Construire le prompt
        prompt = build_temp_rag_prompt(query, context)

        # Ici, vous pouvez intégrer avec votre LLM préféré
        # Pour l'exemple, on utilise une réponse simple
        response = f"Basé sur le contexte fourni: {context[:200]}..."

        # Mettre à jour l'activité utilisateur
        update_user_activity(user_id, db)

        return JSONResponse(
            content={
                "msg": response,
                "context": context_chunks,
                "query": query
            }
        )

    except Exception as e:
        logger.error(f"Erreur dans ask_temp_rag: {e}")
        return JSONResponse(
            content={
                "msg": "Erreur lors du traitement de la question",
                "context": []
            }
        )


@client_router.get("/temp-rag/user-data/{user_id}")
async def get_temp_user_data(
    user_id: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Récupérer les informations des données temporaires d'un utilisateur.
    """
    user_data = get_or_create_user_temp_data(user_id, db)
    uploaded_files = json.loads(user_data.uploaded_files or "[]")

    return JSONResponse(
        content={
            "user_id": user_data.user_id,
            "total_size": user_data.total_size,
            "uploaded_files": uploaded_files,
            "last_activity": user_data.last_activity.isoformat(),
            "expires_at": user_data.expires_at.isoformat()
        }
    )


@client_router.delete("/temp-rag/clear/{user_id}")
async def clear_temp_user_data(
    user_id: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Supprimer toutes les données temporaires d'un utilisateur.
    """
    from src.dev.models.conversation import UserTempData

    user_data = db.query(UserTempData).filter(UserTempData.user_id == user_id).first()
    if user_data:
        db.delete(user_data)
        db.commit()

        return JSONResponse(
            content={"message": "Données utilisateur supprimées avec succès"}
        )

    return JSONResponse(
        content={"message": "Aucune donnée trouvée pour cet utilisateur"}
    )


@client_router.get("/ask-with-temp-docs/{conversation_id}/bot/{bot_id}")
async def ask_with_temp_documents(
    conversation_id: str,
    bot_id: int,
    query: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Poser une question en enrichissant le contexte avec les documents temporaires de la conversation.
    Utilise LangChain + Ollama embeddings pour une recherche vectorielle avancée.
    """
    try:
        # Appeler la logique core existante
        core_result = await core_ask_logic(query, bot_id, db, get_settings())

        # Enrichir le contexte avec les documents temporaires
        enriched_context = enrich_context_with_temp_documents(
            query=query,
            conversation_id=conversation_id,
            db=db,
            existing_context_passages=core_result.get("context", [])
        )

        # Mettre à jour le résultat avec le contexte enrichi
        core_result["context"] = enriched_context
        core_result["temp_docs_used"] = True

        logger.info(f"✅ Réponse générée avec contexte enrichi pour conversation {conversation_id}")

        return JSONResponse(content=core_result)

    except Exception as e:
        logger.error(f"Erreur dans ask_with_temp_documents: {e}")
        return JSONResponse(
            content={
                "msg": "Erreur lors du traitement de la question avec documents temporaires",
                "context": [],
                "temp_docs_used": False
            }
        )


@client_router.get("/temp-docs/list/{conversation_id}")
async def list_temp_documents(
    conversation_id: str,
    db: Session = Depends(get_db),
    user: UserSchema = Depends(get_current_user)
):
    """
    Lister les documents temporaires d'une conversation.
    """
    try:
        from src.dev.models.conversation import ConversationTempDocument

        temp_docs = db.query(ConversationTempDocument).filter(
            ConversationTempDocument.conversationId == conversation_id,
            ConversationTempDocument.expiresAt > datetime.utcnow()
        ).all()

        documents = []
        for doc in temp_docs:
            documents.append({
                "id": doc.id,
                "filename": doc.filename,
                "filepath": doc.filepath,
                "uploadedAt": doc.uploadedAt.isoformat(),
                "expiresAt": doc.expiresAt.isoformat(),
                "uploadedBy": doc.uploadedBy,
                "exists": os.path.exists(doc.filepath) if doc.filepath else False
            })

        return JSONResponse(
            content={
                "conversation_id": conversation_id,
                "documents": documents,
                "total_count": len(documents)
            }
        )

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des documents temporaires: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Erreur lors de la récupération des documents"}
        )