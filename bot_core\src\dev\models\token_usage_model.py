from sqlalchemy import Column, Integer, BigInteger, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base
from datetime import datetime

class TokenUsageLog(Base):
    __tablename__ = 'tokenusagelog'

    logId = Column(Integer, primary_key=True, index=True, autoincrement=True)
    botId = Column(Integer, ForeignKey('bot.idBot'), nullable=False)
    usedInputTokens = Column(BigInteger, nullable=True)
    usedOutputTokens = Column(BigInteger, nullable=True)
    dateTimeLog = Column(DateTime, default=datetime.utcnow, nullable=False)
    maxInputToken = Column(BigInteger, nullable=True)
    maxOutputToken = Column(BigInteger, nullable=True)

    # Relationship
    # bot = relationship("Bot", back_populates="token_usage_logs")

