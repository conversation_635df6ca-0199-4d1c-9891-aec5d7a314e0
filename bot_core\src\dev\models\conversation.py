from datetime import datetime, timed<PERSON>ta
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as SAEnum, Boolean
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base

class Conversation(Base):
    __tablename__ = "conversation"

    conversationId = Column(String(100), primary_key=True)
    userId = Column(Integer, ForeignKey("user.userId"), nullable=False)
    idBot = Column(Integer, ForeignKey("bot.idBot"), nullable=False)
    status = Column(String(20), default='active', nullable=False)  # active, closed, transferred
    startTime = Column(DateTime, default=datetime.utcnow, nullable=False)
    endTime = Column(DateTime, nullable=True)
    assignedAgentId = Column(Integer, ForeignKey("agent.userId"), nullable=True)
    closedByAgentId = Column(Integer, ForeignKey("agent.userId"), nullable=True)
    transferredToAgentId = Column(Integer, Foreign<PERSON>ey("agent.userId"), nullable=True)
    lastActivity = Column(DateTime, default=datetime.utcnow, nullable=False)
    notes = Column(Text, nullable=True)

    user = relationship("User")
    assigned_agent = relationship("Agent", foreign_keys=[assignedAgentId])
    closed_by_agent = relationship("Agent", foreign_keys=[closedByAgentId])
    transferred_to_agent = relationship("Agent", foreign_keys=[transferredToAgentId])
    temp_documents = relationship("ConversationTempDocument", back_populates="conversation")
    events = relationship("ConversationEvent", back_populates="conversation")


class ConversationTempDocument(Base):
    __tablename__ = "conversation_temp_document"

    id = Column(Integer, primary_key=True, autoincrement=True)
    conversationId = Column(String(100), ForeignKey("conversation.conversationId"), nullable=False)
    filename = Column(String(255), nullable=True)
    filepath = Column(Text, nullable=False)
    uploadedAt = Column(DateTime, default=datetime.utcnow, nullable=False)
    expiresAt = Column(DateTime, default=lambda: datetime.utcnow() + timedelta(hours=1), nullable=False)
    uploadedBy = Column(Integer, ForeignKey("user.userId"), nullable=True)

    conversation = relationship("Conversation", back_populates="temp_documents")
    uploader = relationship("User")


class ConversationEvent(Base):
    __tablename__ = "conversation_event"

    id = Column(Integer, primary_key=True, autoincrement=True)
    conversationId = Column(String(100), ForeignKey("conversation.conversationId"), nullable=False)
    eventType = Column(String(50), nullable=False)  # assigned, transferred, closed, reopened, etc.
    agentId = Column(Integer, ForeignKey("agent.userId"), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    details = Column(Text, nullable=True)

    conversation = relationship("Conversation", back_populates="events")
    agent = relationship("Agent")
