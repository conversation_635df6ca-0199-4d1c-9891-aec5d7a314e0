from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from src.dev.utils.dependencies import get_db
from src.dev.models.bot_model import Bot
from src.dev.models.question_response_model import QuestionResponseLog
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import User
from src.dev.models.pack_model import Pack
from sqlalchemy import func

# Replace APIRouter with dashboard_client
dashboard_client = APIRouter()

@dashboard_client.get("/api/v1/dashboard_client/stats")
def get_client_stats(date_filter: str, client_id: int, db: Session = Depends(get_db)):
    if date_filter not in ['day', 'month', 'year']:
        raise HTTPException(status_code=400, detail="Invalid date filter")

    now = datetime.now()
    if date_filter == 'day':
        start_date = now - timedelta(days=1)
    elif date_filter == 'month':
        start_date = now - timedelta(days=30)
    elif date_filter == 'year':
        start_date = now - timedelta(days=365)

    # Calculer les visiteurs uniques
    unique_visitors = (
        db.query(QuestionResponseLog.userId)
        .join(Bot, Bot.idBot == QuestionResponseLog.idBot)  # Joindre la table Bot sur l'id de souscription
        .join(Subscription, Subscription.idSubscription == Bot.idSubscription)  # Joindre la table Souscription sur l'id de souscription
        .filter(
            QuestionResponseLog.timeQuestion >= start_date,  # Filtrer par date de début
            Subscription.userId == client_id  # Vérifier que le userId de la souscription correspond à id_client
        )
        .distinct().count()  # Renvoyer le nombre de visiteurs uniques
    )

    # Calculer le nombre total de questions
    total_questions = (
        db.query(QuestionResponseLog.question)
        .join(Bot, Bot.idBot == QuestionResponseLog.idBot)  # Joindre la table Bot sur l'id de souscription
        .join(Subscription, Subscription.idSubscription == Bot.idSubscription)  # Joindre la table Souscription sur l'id de souscription
        .filter(
            QuestionResponseLog.timeQuestion >= start_date,  # Filtrer par date de début
            Subscription.userId == client_id  # Vérifier que le userId de la souscription correspond à id_client
        )
        .count()  # Renvoyer le nombre total de questions
    )

    # Calculer les jetons utilisés
    tokens_used = db.query(
        Bot.botName,
        Bot.currentInputToken,
        Bot.currentOutputToken,
        (Pack.maxInputToken - func.coalesce(Bot.currentInputToken, 0)).label('remainingInputTokens'),
        (Pack.maxOutputToken - func.coalesce(Bot.currentOutputToken, 0)).label('remainingOutputTokens')
    ).join(Subscription, Subscription.idSubscription == Bot.idSubscription) \
    .join(Pack, Pack.idPack == Subscription.idPack) \
    .join(User, User.userId == Subscription.userId) \
    .filter(User.userId == client_id) \
    .all()

    # Obtenir les détails de souscription
    subscription_details = db.query(
        Subscription.idPack,
        func.count(Bot.idBot).label('numberOfBots'),
        Subscription.status
    ).join(Bot, Bot.idSubscription == Subscription.idSubscription) \
    .filter(Subscription.userId == client_id) \
    .group_by(Subscription.idPack, Subscription.status) \
    .all()

    # Créer une liste pour accumuler les détails des souscriptions
    subscription_details_list = []

    # Traiter les résultats pour chaque pack
    if subscription_details:
        for detail in subscription_details:
            pack = detail[0]
            number_of_bots = detail[1]
            status = detail[2]
            # Ajouter chaque détail de souscription à la liste
            subscription_details_list.append({
                "pack": pack,
                "number_of_bots": number_of_bots,
                "status": status
            })
    else:
        # Si aucun détail de souscription n'est trouvé, retourner une liste vide
        subscription_details_list = []

    # Retourner la réponse JSON avec les informations calculées
    return {
        "unique_visitors": unique_visitors,
        "total_questions": total_questions,
        "tokens_used": {
            bot.botName: {
                "input": bot.currentInputToken,
                "output": bot.currentOutputToken,
                "remaining_input": bot.remainingInputTokens,
                "remaining_output": bot.remainingOutputTokens
            } for bot in tokens_used
        },
        "subscription_details": subscription_details_list
    }
