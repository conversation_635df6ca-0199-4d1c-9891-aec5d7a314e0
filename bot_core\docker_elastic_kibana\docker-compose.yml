services:
  elasticsearch:
    container_name: elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.15.0
    volumes:
      - elasticdata:/usr/share/elasticsearch/data
    environment:
      - bootstrap.memory_lock=true
      - discovery.type=single-node
      - network.host=0.0.0.0  # Listen on all network interfaces
      - http.port=16405  # Port configuration for Elasticsearch
      # ✅ Disable security
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - xpack.security.http.ssl.enabled=false

    ports:
      - 16405:16405  # Expose Elasticsearch on port 16405
    networks:
      - elastic
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536

  
  kibana:
    image: docker.elastic.co/kibana/kibana:8.15.0
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://************:16405  # Point Kibana to Elasticsearch
      - SERVER_HOST=0.0.0.0  # Allow Ki<PERSON> to bind to all interfaces
      - SERVER_PORT=16407  # Set Kibana to use port 16407
      # ✅ Disable Kibana security requirement
      - xpack.security.enabled=false
    ports:
      - 16407:16407  # Expose Kibana on port 16407
    networks:
      - elastic
    depends_on:
      - elasticsearch

networks:
  elastic:

volumes:
  elasticdata:
