import os

def find_project_root(current_dir :str):
    """
    Finds the root directory.

    Returns:
        str: The path to the root directory.
    """

    while "main.py" not in os.listdir(current_dir):
        current_dir = os.path.dirname(current_dir)

        # Break the loop if we reach the root of the filesystem
        if current_dir == os.path.dirname(current_dir):
            raise ValueError("Unable to find the root directory of the project.")
    
    return current_dir

