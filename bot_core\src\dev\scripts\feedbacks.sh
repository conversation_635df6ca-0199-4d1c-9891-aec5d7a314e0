#!/bin/bash

# Charger les variables du fichier .env s'il existe
ENV_PATH="/Users/<USER>/workspace/.env"
if [ -f "$ENV_PATH" ]; then
    set -a
    source "$ENV_PATH"
    set +a
else
    echo "[ERREUR] Fichier .env introuvable dans $(realpath $ENV_PATH)"
    exit 1
fi

# Vérifier que BOT_CORE est bien chargée
if [ -z "$BOT_CORE" ]; then
    echo "[ERREUR] BOT_CORE n'est pas définie dans .env"
    exit 1
fi
feedbacks_log=$BOT_CORE/src/dev/scripts/feedbacks.log

cd $BOT_CORE &&
# source venv/Scripts/activate && # pour windows
# source .venv/bin/activate && #pour linux/mac
python -m src.dev.scripts.feedbacks # >> $feedbacks_log


