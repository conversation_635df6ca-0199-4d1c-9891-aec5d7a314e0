
from typing import List

from fastapi import APIRouter, Body, Depends, Response, status
from sqlalchemy import update
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import Role<PERSON>hecker
from src.dev.api.controllers import visitor_controller as controller
from src.dev.api.controllers.visitor_controller import get_visitors, get_visitor, update_visitor, \
    delete_visitor
from src.dev.models.question_response_model import QuestionResponseLog
from src.dev.schemas.feedback_schema import FeedbackSchema
from src.dev.schemas.visitor import VisitorOut, VisitorUpdate
from src.dev.schemas.visitor_schema import CreateVisitorSchema, VisitorSchema
from src.dev.services.auth.auth import AuthService, get_authenticated_visitor_from_session_id
from src.dev.utils.dependencies import get_db

router = APIRouter()


@router.get("/visitors/", response_model=List[VisitorOut])
def read_visitors(skip: int = 0, limit: int = 10, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return get_visitors(db, skip=skip, limit=limit)

@router.get("/visitors/{visitor_id}", response_model=VisitorOut)
def read_visitor(visitor_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return get_visitor(db, visitor_id)


@router.delete("/visitors/{visitor_id}")
def delete_visitor(visitor_id: str,
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return controller.delete_visitor(visitor_id)

@router.post("/login")
def submit_email(response: Response, email: str = Body(embed=True), session: Session = Depends(get_db)):
    return AuthService(session).login_visitor(email, response)

@router.post("/sign-up", response_model=VisitorSchema)
def create_account(response: Response, visitor: CreateVisitorSchema, session: Session = Depends(get_db)):
    response.status_code = status.HTTP_201_CREATED
    return AuthService(session).create_account(visitor)

# TODO: Refactor to spepartor file. 
@router.put("/feedback")
async def send_feedback(feedback: FeedbackSchema, 
                        session: Session = Depends(get_db), 
                        visitor: VisitorSchema = Depends(get_authenticated_visitor_from_session_id)
    ):
    update_stmt = (
        update(QuestionResponseLog)
        .where(
            QuestionResponseLog.timeQuestion == feedback.timestamp
            and QuestionResponseLog.idBot == feedback.idBot
            and QuestionResponseLog.userId == visitor.user_id
        )
        .values(feedback=feedback.feedback, feedbackText=feedback.feedbackText)
    )
    model = session.execute(update_stmt)
    session.commit()

    return {"message": "votre feedback est bien sauvegardé"}

#@router.post("/visitors/", response_model=VisitorOut)
#def create_new_visitor(visitor: VisitorCreate, db: Session = Depends(get_db)):
#    return create_visitor(db, VisitorModel(**visitor.dict()))

@router.put("/visitors/{visitor_id}", response_model=VisitorOut)
def update_existing_visitor(visitor_id: int, visitor_update: VisitorUpdate, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return update_visitor(db, visitor_id, visitor_update)


@router.delete("/visitors/{visitor_id}")
def delete_existing_visitor(visitor_id: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return delete_visitor(visitor_id, db)

