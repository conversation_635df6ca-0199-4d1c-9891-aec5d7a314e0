import re
import pickle
import logging
import numpy as np
from typing import List, Optional, Union, Any, Dict, Tuple

# FastAPI and Database
from fastapi import HTTPException
from sqlalchemy.orm import Session

# Text processing libraries
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import WebBaseLoader

# Embedding libraries
from sentence_transformers import SentenceTransformer, CrossEncoder

# Reranking and text processing
from rank_bm25 import BM25Okapi
from nltk.tokenize import word_tokenize
import nltk
import json

# LlamaIndex for document processing (for type compatibility)
from llama_index.core import Document

# for casting types
from typing import cast
# Local imports
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.services.rag.rag_processor import AbstractRAGProcessor

# Download NLTK resources
nltk.download('punkt', quiet=True)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LangChainRAGProcessor(AbstractRAGProcessor):
    def __init__(self, 
                 embedding_model_name: str = "nomic-ai/nomic-embed-text-v1.5",
                 reranker_model_name: str = "BAAI/bge-reranker-base"):
        """
        Initialize RAG Processor with SentenceTransformer and CrossEncoder for reranking
        """
        self.logger = logging.getLogger(__name__)
        try:
            self.sentence_transformer = SentenceTransformer(embedding_model_name, trust_remote_code=True)
            self.reranker = CrossEncoder(reranker_model_name)
            self.logger = logger
            self.embedding_dim = 768 #ettg fixed dimension for Nomic embeddings
            logger.info(f"Initialized LangChainRAGProcessor with {embedding_model_name} embedding model")
        except Exception as e:
            self.logger.error(f"Error initializing models: {e}", exc_info=True)
            raise

    def preprocess_text(self, text: str) -> str:
        """
        Preprocess the input text by cleaning and normalizing
        """
        try:
            if text is None:
                return ""
                
            # Replace tabs with spaces
            text = text.replace('\t', ' ')
            
            # Handle numeric points
            text = re.sub(r'(\d+)\n\.', r'\1.', text)
            
            # Replace newlines that might break sentences
            text = re.sub(r'(?<![\d]\.)\n(?=[A-Z])', ' ', text)
            
            # Replace remaining newlines with spaces
            text = text.replace('\n', ' ')
            
            # Remove extra whitespaces
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
        except Exception as e:
            self.logger.error(f"Error in text preprocessing: {e}")
            return text if text else ""

    def semantic_splitter_with_pages(self, text: Union[str, List[str], List[Document]],
                                   chunk_size: int = 1024,
                                   page_number: Optional[int] = None) -> List[Tuple[str, Optional[int]]]:
        """
        Semantically split text into chunks with page numbers.
        
        Args:
            text (Union[str, List[str], List[Document]]): Input text to split
            chunk_size (int): Size of each chunk
            page_number (Optional[int]): Page number for single text input

        Returns:
            List[Tuple[str, Optional[int]]]: List of (chunk_text, page_number) tuples
        """
        logger.debug(f"semantic_splitter_with_pages input type: {type(text)}")
        
        # Handle LlamaIndex Document objects
        if isinstance(text, list) and text and all(isinstance(doc, Document) for doc in text):
            logger.info(f"Converting {len(text)} LlamaIndex Documents to text")
            doc_list = cast(List[Document], text)

            all_chunks = []
            for i, doc in enumerate(doc_list):
                # Try to get page number from document metadata
                doc_page = getattr(doc, 'metadata', {}).get('page_number', i + 1)
                doc_chunks = self.semantic_splitter_with_pages(doc.text, chunk_size, doc_page)
                all_chunks.extend(doc_chunks)
            return all_chunks
        
        # Handle empty input
        if not text:
            logger.warning("Empty text provided to semantic_splitter_with_pages")
            return []
            
        # Handle list of texts
        if isinstance(text, list):
            all_chunks = []
            for i, doc_text in enumerate(text):
                if doc_text and isinstance(doc_text, str):
                    # Use index as page number if none provided
                    current_page = page_number if page_number is not None else i + 1
                    doc_chunks = self.semantic_splitter_with_pages(doc_text, chunk_size, current_page)
                    all_chunks.extend(doc_chunks)
            return all_chunks
            
        # Handle single text
        if not isinstance(text, str):
            logger.warning(f"Expected string, got {type(text)} in semantic_splitter_with_pages")
            return []
            
        # Skip empty strings
        if not text.strip():
            return []
            
        # Preprocess text
        preprocessed_text = self.preprocess_text(text)
        if not preprocessed_text:
            return []
            
        # Create text splitter
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=100,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
        
        # Split text into chunks
        try:
            chunks = text_splitter.split_text(preprocessed_text)
            logger.info(f"Split text into {len(chunks)} chunks for page {page_number}")
            # Return chunks with page numbers
            return [(chunk, page_number) for chunk in chunks]
        except Exception as e:
            logger.error(f"Error splitting text: {e}")
            # Fallback to simple splitting if advanced splitting fails
            simple_chunks = [preprocessed_text[i:i+chunk_size] for i in range(0, len(preprocessed_text), chunk_size)]
            return [(chunk, page_number) for chunk in simple_chunks]

    def semantic_splitter(self, text: Union[str, List[str], List[Document]], chunk_size: int = 1024) -> List[str]:
        """
        Backward compatibility method - splits text without page numbers
        """
        chunks_with_pages = self.semantic_splitter_with_pages(text, chunk_size)
        return [chunk for chunk, _ in chunks_with_pages]

    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a given text
        """
        try:
            # Handle empty text
            if not text or not text.strip():
                # Return zero vector for empty text
                return [0.0] * self.embedding_dim
                
            # Preprocess text
            preprocessed_text = self.preprocess_text(text)
            
            # Generate embedding
            embedding = self.sentence_transformer.encode(preprocessed_text, convert_to_tensor=False)
            
            # Handle case where embedding might be a list of arrays/tensors
            if isinstance(embedding, list):
             # If it's a list, take the first element
                embedding = embedding[0]

            # Convert to numpy array if it's a tensor
            if not isinstance(embedding, np.ndarray):
                # Check if it's a PyTorch tensor
                if hasattr(embedding, 'cpu') and hasattr(embedding, 'detach'):
                    # It's a PyTorch tensor
                    embedding = embedding.detach().cpu().numpy()
                # Check if it's a TensorFlow tensor
                elif hasattr(embedding, 'numpy') and str(type(embedding)).find('tensorflow') != -1:
                    # It's a TensorFlow tensor
                    embedding = embedding.numpy()
                else:
                    # Convert to numpy array for other types
                    embedding = np.array(embedding)
            
            # Ensure fixed dimension size
            if len(embedding) > self.embedding_dim:
                embedding = embedding[:self.embedding_dim]
            elif len(embedding) < self.embedding_dim:
                # Pad with zeros if less than expected dimension
                embedding = np.pad(embedding, (0, self.embedding_dim - len(embedding)), mode='constant')
            
            # Convert to list of floats
            return embedding.tolist()
        
        except Exception as e:
            logger.error(f"Embedding generation error: {e}")
            # Return zero vector in case of error
            return [0.0] * self.embedding_dim

    def process_document_with_pages(self, document_text: Union[str, List[str], List[Document]],
                                  page_numbers: Optional[List[int]] = None) -> Tuple[List[str], List[List[float]], List[Optional[int]]]:
        """
        Process document to extract chunks and generate embeddings with page numbers
        
        Args:
            document_text (Union[str, List[str], List[Document]]): Document text or list of texts/Documents to process
            page_numbers (Optional[List[int]]): List of page numbers corresponding to each document text

        Returns:
            Tuple[List[str], List[List[float]], List[Optional[int]]]: Tuple of chunks, embeddings, and page numbers
        """
        try:
            # Handle page numbers for list input
            if isinstance(document_text, list) and page_numbers is None:
                page_numbers = list(range(1, len(document_text) + 1))
            elif isinstance(document_text, str):
                page_numbers = [1]  # Default to page 1 for single text

            # Split text into chunks with page numbers
            if isinstance(document_text, list):
                all_chunks_with_pages = []
                for i, text in enumerate(document_text):
                    page_num = page_numbers[i] if page_numbers and i < len(page_numbers) else i + 1
                    chunks_with_pages = self.semantic_splitter_with_pages(text, page_number=page_num)
                    all_chunks_with_pages.extend(chunks_with_pages)
            else:
                page_num = page_numbers[0] if page_numbers else 1
                all_chunks_with_pages = self.semantic_splitter_with_pages(document_text, page_number=page_num)

            # Separate chunks and page numbers
            chunks = [chunk for chunk, _ in all_chunks_with_pages]
            chunk_page_numbers = [page_num for _, page_num in all_chunks_with_pages]
            
            # Generate embeddings for each chunk
            embeddings = [self.generate_embedding(chunk) for chunk in chunks]
            
            return chunks, embeddings, chunk_page_numbers
        except Exception as e:
            logger.error(f"Error processing document with pages: {e}")
            return [], [], []

    def process_document(self, document_text: Union[str, List[str], List[Document]]) -> Tuple[List[str], List[List[float]]]:
        """
        Backward compatibility method - process document without page numbers
        """
        chunks, embeddings, _ = self.process_document_with_pages(document_text)
        return chunks, embeddings
    
    def node_embedding(self, documents: Union[List[str], List[Document]]) -> List[List[float]]:
        """
        Generate embeddings for documents or text chunks
        
        Args:
            documents (Union[List[str], List[Document]]): List of text chunks or Document objects
        
        Returns:
            List[List[float]]: List of embeddings
        """
        embeddings = []
        
        # Handle LlamaIndex Document objects
        if documents and isinstance(documents[0], Document):
            # Extract text from Document objects
            chunks = []
            for doc in documents:
                if isinstance(doc, Document) and hasattr(doc, 'text'):
                    chunks.append(doc.text)
                else:
                    logger.warning(f"Skipping invalid document of type {type(doc)}")
        else:
            # Assume they are strings
            chunks = documents
            
        # Generate embeddings for each chunk
        for chunk in chunks:
            if isinstance(chunk, str):
                embedding = self.generate_embedding(chunk)
                embeddings.append(embedding)
            else:
                logger.warning(f"Skipping non-string chunk of type {type(chunk)}")
                
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    
    def vectorize_document_and_store_in_database(
        self, document_url: str, bot_id: int, doc_id: int, db: Session,
        document_pages: Optional[List[Tuple[str, int]]] = None
    ):
        """
        Vectorize document and store in database with page numbers
        
        Args:
            document_url (str): URL of the document to vectorize
            bot_id (int): ID of the bot
            doc_id (int): ID of the document
            db (Session): Database session
            document_pages (Optional[List[Tuple[str, int]]]): List of (text, page_number) tuples

        Returns:
            None
        """
        try:
            if document_pages:
                # Use provided document pages
                all_chunks_with_pages = []
                for text, page_num in document_pages:
                    chunks_with_pages = self.semantic_splitter_with_pages(text, page_number=page_num)
                    all_chunks_with_pages.extend(chunks_with_pages)

                chunks = [chunk for chunk, _ in all_chunks_with_pages]
                page_numbers = [page_num for _, page_num in all_chunks_with_pages]
                embeddings = [self.generate_embedding(chunk) for chunk in chunks]
            else:
                # Load document from URL (fallback)
                loader = WebBaseLoader(document_url)
                documents = loader.load()

                # Extract text from documents
                texts = [doc.page_content for doc in documents]

                # Process document to extract chunks and generate embeddings with pages
                chunks, embeddings, page_numbers = self.process_document_with_pages(texts)
            
            # Store in database with tokenized text for BM25 and page numbers
            for chunk, embedding, page_num in zip(chunks, embeddings, page_numbers):
                # Tokenize chunk for BM25 retrieval
                tokens = self._tokenize_chunk(chunk)
                
                embedding_data = EmbeddingData(
                    botId=bot_id,
                    docId=doc_id,
                    chunkText=chunk,
                    embeddings=embedding,
                    tokenized_tokens=json.dumps(tokens),  # Store tokenized text for BM25
                    docUrl=document_url,  # Store URL for reference
                    pageNumber=page_num  # Store page number
                )
                db.add(embedding_data)
            
            db.commit()
            logger.info(f"Successfully vectorized document with ID {doc_id} for bot {bot_id} with {len(chunks)} chunks")
        except Exception as e:
            db.rollback()
            logger.error(f"Error vectorizing document: {e}")
            raise HTTPException(status_code=500, detail=f"Error vectorizing document: {str(e)}")
    
    def _tokenize_chunk(self, chunk_text: str) -> List[str]:
        """
        Tokenize chunk for BM25 retrieval
        """
        return word_tokenize(self.preprocess_text(chunk_text).lower())
        
    def rerank_passages(self, query: str, passages: List[str]) -> List[str]:
        """
        Rerank retrieved passages using CrossEncoder
        
        Args:
            query (str): The query
            passages (List[str]): List of retrieved passages
            
        Returns:
            List[str]: Reranked passages
        """
        if not passages:
            return []
            
        try:
            # Create pairs of query and passages for scoring (as lists, not tuples)
            pairs = [[query, passage] for passage in passages]
            
            # Score all pairs
            scores = self.reranker.predict(pairs)
            
            # Convert scores to float and create scored passages
            scored_passages = [(passage, float(score)) for passage, score in zip(passages, scores)]
            
            # Sort passages by score in descending order
            reranked = [passage for passage, score in sorted(scored_passages, key=lambda x: x[1], reverse=True)]
            
            logger.info(f"Reranked {len(passages)} passages")
            return reranked[:min(3, len(reranked))]  # Return top 3 or fewer
        except Exception as e:
            logger.error(f"Error in reranking: {e}")
            return passages[:min(3, len(passages))]  # Fallback to original order
    
    def hybrid_retrieval_with_pages(self, query: str, chunks: List[str],
                                   tokenized_corpus: List[List[str]],
                                   page_numbers: List[Optional[int]],
                                   top_k: int = 5) -> List[Tuple[str, Optional[int]]]:
        """
        Hybrid retrieval using both vector similarity and BM25 with page numbers
        
        Args:
            query (str): Query text
            chunks (List[str]): List of text chunks
            tokenized_corpus (List[List[str]]): Tokenized text chunks for BM25
            page_numbers (List[Optional[int]]): Page numbers for each chunk
            top_k (int): Number of results to return
            
        Returns:
            List[Tuple[str, Optional[int]]]: Retrieved passages with page numbers
        """
        try:
            # Vector retrieval: Generate embedding for query
            query_embedding = self.generate_embedding(query)
            
            # Compute similarities for each chunk
            similarities = []
            for i, chunk in enumerate(chunks):
                chunk_embedding = self.generate_embedding(chunk)
                # Compute cosine similarity
                similarity = np.dot(query_embedding, chunk_embedding) / (
                    np.linalg.norm(query_embedding) * np.linalg.norm(chunk_embedding)
                )
                similarities.append((i, similarity))
            
            # Get top vector results
            vector_indices = [idx for idx, _ in sorted(similarities, key=lambda x: x[1], reverse=True)[:top_k]]
            vector_passages = [(chunks[idx], page_numbers[idx]) for idx in vector_indices]
            
            # BM25 retrieval
            try:
                bm25 = BM25Okapi(tokenized_corpus)
                tokenized_query = word_tokenize(query.lower())
                scores = bm25.get_scores(tokenized_query)
                bm25_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)[:top_k]
                bm25_passages = [(chunks[i], page_numbers[i]) for i in bm25_indices]
                
                # Combine results (remove duplicates based on text)
                seen_chunks = set()
                combined = []
                for passage, page_num in vector_passages + bm25_passages:
                    if passage not in seen_chunks:
                        seen_chunks.add(passage)
                        combined.append((passage, page_num))
                
                logger.info(f"Hybrid retrieval returned {len(combined)} unique passages")
                return combined[:top_k]
            except Exception as e:
                logger.error(f"BM25 retrieval failed: {e}, falling back to vector retrieval")
                return vector_passages
                
        except Exception as e:
            logger.error(f"Error in hybrid retrieval: {e}")
            # Fallback to direct chunk retrieval
            return [(chunks[i], page_numbers[i]) for i in range(min(top_k, len(chunks)))]
    
    def hybrid_retrieval(self, query: str, chunks: List[str], tokenized_corpus: List[List[str]], top_k: int = 5) -> List[str]:
        """
        Backward compatibility method - hybrid retrieval without page numbers
        """
        page_numbers = [None] * len(chunks)
        passages_with_pages = self.hybrid_retrieval_with_pages(query, chunks, tokenized_corpus, page_numbers, top_k)
        return [passage for passage, _ in passages_with_pages]

    def load_chunks_with_tokens_and_pages(self, bot_id: int, db: Session) -> dict:
        """
        Load text chunks, their tokenized versions, and page numbers for a specific bot
        
        Args:
            bot_id: The ID of the bot
            db: Database session
        
        Returns:
            A dictionary containing chunks, tokenized corpus, and page numbers
        """
        try:
            # Download NLTK data if not already downloaded
            nltk.download('punkt', quiet=True)
        except:
            logger.warning("Unable to download NLTK data, proceeding with existing resources")
        
        logger.info(f"Loading chunks with pages for bot {bot_id}")
        
        # Query the database for chunks related to this bot
        chunk_records = db.query(
            EmbeddingData.chunkText,
            EmbeddingData.tokenized_tokens,
            EmbeddingData.pageNumber
        ).filter(EmbeddingData.botId == bot_id).all()
        
        # Extract chunks from the database records
        chunks = []
        tokenized_corpus = []
        page_numbers = []
        #TODO a vérifier la logique de retour de requete
        for record in chunk_records:
            chunk_text = record[0]
            tokenized_tokens = record[1]
            page_number = record[2]

            chunks.append(chunk_text)
            page_numbers.append(page_number)

            # Handle tokenized tokens
            if tokenized_tokens is not None:
                try:
                    # Try to parse JSON stored tokens
                    tokens = json.loads(tokenized_tokens)
                    tokenized_corpus.append(tokens)
                except (json.JSONDecodeError, TypeError):
                    # If JSON parsing fails, tokenize on the fly
                    tokens = self._tokenize_chunk(chunk_text)
                    tokenized_corpus.append(tokens)
            else:
                # If no stored tokens, tokenize on the fly
                tokens = self._tokenize_chunk(chunk_text)
                tokenized_corpus.append(tokens)
        
        if not chunks:
            logger.warning(f"No chunks found for bot {bot_id}")
            raise HTTPException(status_code=404, detail=f"No document chunks found for bot {bot_id}")
        
        logger.info(f"Found {len(chunks)} chunks for bot {bot_id}")
        
        return {
            "chunks": chunks,
            "tokenized_corpus": tokenized_corpus,
            "page_numbers": page_numbers
        }

    def load_chunks_with_tokens(self, bot_id: int, db: Session) -> dict:
        """
        Backward compatibility method - load chunks without page numbers
        """
        data = self.load_chunks_with_tokens_and_pages(bot_id, db)
        return {
            "chunks": data["chunks"],
            "tokenized_corpus": data["tokenized_corpus"]
        }
    
    def process_rag_query_with_pages(self, prompt: str, bot_id: int, db: Session) -> dict:
        """
        Process RAG query with page information
        
        Args:
            prompt (str): Query prompt
            bot_id (int): Bot ID
            db (Session): Database session
            
        Returns:
            dict: Response with page information
        """
        try:
            # Load chunks, tokenized corpus, and page numbers from database
            data = self.load_chunks_with_tokens_and_pages(bot_id, db)
            chunks = data["chunks"]
            tokenized_corpus = data["tokenized_corpus"]
            page_numbers = data["page_numbers"]

            if not chunks:
                return {
                    "response": "No relevant information found for your query.",
                    "sources": [],
                    "page_numbers": []
                }

            # Hybrid retrieval with pages
            retrieved_passages_with_pages = self.hybrid_retrieval_with_pages(
                prompt, chunks, tokenized_corpus, page_numbers, top_k=5
            )

            # Extract passages for reranking
            retrieved_passages = [passage for passage, _ in retrieved_passages_with_pages]

            # Rerank passages
            reranked_passages = self.rerank_passages(prompt, retrieved_passages)

            # Get page numbers for reranked passages
            reranked_page_numbers = []
            for reranked_passage in reranked_passages:
                for passage, page_num in retrieved_passages_with_pages:
                    if passage == reranked_passage:
                        reranked_page_numbers.append(page_num)
                        break

            # Combine context
            context = "\n\n".join(reranked_passages)

            # Create sources with page information
            sources = []
            for passage, page_num in zip(reranked_passages, reranked_page_numbers):
                source_info = {
                    "text": passage[:200] + "..." if len(passage) > 200 else passage,
                    "page_number": page_num
                }
                sources.append(source_info)

            return {
                "response": f"Here is information relevant to your query:\n\n{context}",
                "sources": sources,
                "page_numbers": list(set(filter(None, reranked_page_numbers)))  # Unique page numbers
            }

        except Exception as e:
            logger.error(f"Error processing RAG query with pages: {e}")
            raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

    def process_rag_query(self, prompt: str, document_key: str, db: Session):
        """
        Backward compatibility method - process RAG query without page numbers
        """
        try:
            # Load chunks and tokenized corpus from database
            results = (
                db.query(EmbeddingData.chunkText, EmbeddingData.tokenized_tokens)
                .filter(EmbeddingData.docUrl == document_key)
                .all()
            )
            
            if not results:
                return "No relevant information found for your query."
            
            chunks = []
            tokenized_corpus = []
            
            for r in results:
                chunks.append(r.chunkText)
                # Handle tokenized tokens
                if r.tokenized_tokens is not None:
                    try:
                        tokenized_corpus.append(json.loads(r.tokenized_tokens))
                    except json.JSONDecodeError:
                        # Tokenize on the fly if needed
                        tokenized_corpus.append(self._tokenize_chunk(r.chunkText))
                else:
                    # Tokenize on the fly
                    tokenized_corpus.append(self._tokenize_chunk(r.chunkText))
            
            # Hybrid retrieval
            retrieved_passages = self.hybrid_retrieval(prompt, chunks, tokenized_corpus, top_k=5)
            
            # Rerank passages
            reranked_passages = self.rerank_passages(prompt, retrieved_passages)
            
            # Combine context
            context = "\n\n".join(reranked_passages)
            
            # This is a simplified response - in a real system, you would feed this to an LLM
            # and generate a proper response
            response = f"Here is information relevant to your query:\n\n{context}"
            
            return response
        except Exception as e:
            logger.error(f"Error processing RAG query: {e}")
            raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

    def query_embedding(self, query: str) -> List[float]:
        """
        Generate embedding for a query
        """
        if not query or not query.strip():
            return [0.0] * self.embedding_dim
            
        return self.generate_embedding(query)
