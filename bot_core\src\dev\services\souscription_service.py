# crud.py
from fastapi import HTTPException
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from src.dev.models.pack_model import Pack
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import User
from src.dev.schemas.subscription_schemas import SouscriptionCreate, SouscriptionUpdate


def create_souscription(souscription: SouscriptionCreate, db: Session):
    db_user = db.query(User).filter(User.userId == souscription.userId).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User n'existe pas")

    db_pack = db.query(Pack).filter(Pack.idPack == souscription.idPack).first()
    if db_pack is None:
        raise HTTPException(status_code=404, detail="Pack n'existe pas")

    db_souscription = Subscription(
        idPack=souscription.idPack,
        userId=souscription.userId,
        startDate=souscription.startDate.replace(microsecond=0),
        endDate=souscription.endDate.replace(microsecond=0),
        status=souscription.status,
    )
    try:
        db.add(db_souscription)
        db.commit()
        db.refresh(db_souscription)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Souscription avec cet identifiant existe déjà")
    return db_souscription

def update_souscription(idSubscription: int, souscription: SouscriptionUpdate, db: Session):
    db_souscription = db.query(Subscription).filter(Subscription.idSubscription == idSubscription).first()
    if db_souscription is None:
        raise HTTPException(status_code=404, detail="Souscription not found")

    db_user = db.query(User).filter(User.userId == souscription.userId).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User n'existe pas")

    db_pack = db.query(Pack).filter(Pack.idPack == souscription.idPack).first()
    if db_pack is None:
        raise HTTPException(status_code=404, detail="Pack n'existe pas")

    db_souscription.idPack = souscription.idPack
    db_souscription.userId = souscription.userId
    db_souscription.startDate = souscription.startDate.replace(microsecond=0)
    db_souscription.endDate = souscription.endDate.replace(microsecond=0)
    db_souscription.status = souscription.status
    db.commit()
    db.refresh(db_souscription)
    return db_souscription

def delete_souscription(idSubscription: int, db: Session):
    db_souscription = db.query(Subscription).filter(Subscription.idSubscription == idSubscription).first()
    if db_souscription is None:
        raise HTTPException(status_code=404, detail="Souscription not found")
    try:
        db.delete(db_souscription)
        db.commit()
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Erreur de suppression: La souscription est encore référencée")
    return {"detail": "Souscription deleted"}

def get_souscriptions(db: Session):
    return db.query(Subscription).all()
