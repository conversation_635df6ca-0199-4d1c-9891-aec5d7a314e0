from sqlalchemy.orm import Session
from fastapi import HTTPException, logger
from src.dev.models.user_model import Visitor as VisitorModel
from datetime import datetime
from src.dev.schemas.visitor import VisitorUpdate

def get_visitors(db: Session, skip: int = 0, limit: int = 200):
    return db.query(VisitorModel).offset(skip).limit(limit).all()

def get_visitor(db: Session, visitor_id: int):
    visitor = db.query(VisitorModel).filter(VisitorModel.userId == visitor_id).first()
    if not visitor:
        raise HTTPException(status_code=404, detail="Visitor not found")
    return visitor

#def create_visitor(db: Session, visitor: VisitorModel):
#    db.add(visitor)
#    db.commit()
#    db.refresh(visitor)
#    return visitor

def update_visitor(db: Session, visitor_id: int, visitor_update: VisitorUpdate):
    db_visitor = db.query(VisitorModel).filter(VisitorModel.userId == visitor_id).first()
    if not db_visitor:
        raise HTTPException(status_code=404, detail="Visitor not found")

    for key, value in visitor_update.dict().items():
        setattr(db_visitor, key, value)
    
    db.commit()
    db.refresh(db_visitor)
    return db_visitor

def delete_visitor(visitor_id: int, db: Session):
    db_visitor = db.query(VisitorModel).filter(VisitorModel.userId == visitor_id).first()
    if not db_visitor:
        raise HTTPException(status_code=404, detail="Visitor not found")
    
    db.delete(db_visitor)
    db.commit()
    return {"detail": "Visitor deleted"}