from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import <PERSON><PERSON><PERSON><PERSON>
from src.dev.models.pack_model import Pack
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import User
from src.dev.utils.dependencies import get_db

user_pack_router = APIRouter()

@user_pack_router.get("/api/v1/user_pack")
def get_user_pack_data(db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    # Retrieve all users with their IDs and names
    users = db.query(User.userId, User.userName).all()
    # Retrieve all packs with their IDs and names
    packs = db.query(Pack.idPack, Pack.packName).all()
    # Retrieve all unique subscription statuses
    subscriptions = db.query(Subscription.status).distinct().all()

    if not users and not packs and not subscriptions:
        raise HTTPException(status_code=404, detail="No data found")

    # Prepare the output format
    user_data = [{"id": user_id, "name": user_name} for user_id, user_name in users]
    pack_data = [{"id": pack_id, "name": pack_name} for pack_id, pack_name in packs]
    # Convert to a set to ensure unique statuses, then convert back to list
    unique_statuses = {sub_status for (sub_status,) in subscriptions}
    subscription_data = [{"status": status} for status in unique_statuses]

    return {
        "users": user_data,
        "packs": pack_data,
        "statuses": subscription_data  # Include unique subscription statuses in the response
    }
