from sqlalchemy import update
from enum import Enum

from elasticsearch import Elasticsearch
from sqlalchemy import update
from sqlalchemy.orm import Session
from src.dev.config.config import Settings, get_settings
from src.dev.models.question_response_model import FeedbackStatus, QuestionResponseLog
from src.dev.services.rag.rag_processor_factory import get_rag_processor
from src.dev.utils.database import SessionLocal
from src.dev.const import ERROR_LOG, INFO_LOG, WAR_LOG


def get_questionresponse_logs(session, search_condition) -> QuestionResponseLog:
  questions = (
    session.query(QuestionResponseLog)
    .where(search_condition)
    .order_by(QuestionResponseLog.idLog)
    .all()
  )
  return questions
  
def mark_reviewed_feedbacks_as_treated(session: Session):
  update_stmt = (
    update(QuestionResponseLog)
    .where(QuestionResponseLog.feedback == FeedbackStatus.REVISEE.value)
    .values(feedback=FeedbackStatus.TRAITEE.value)
  )
  session.execute(update_stmt)
  session.commit()

def create_questions_index(es):
  mappings = {
    "properties": {
            "idBot": {"type": "integer"},
            "idLog": {"type": "integer"},
            "question": {"type": "keyword"},
            "response": {"type": "keyword"},
            "langue": {"type": "keyword"},
            "responseSource": {"type": "keyword"},
            "feedback": {"type": "keyword"},
            "question_embedding": {
                "type": "dense_vector",
                "dims": 768
            }
    }
  }
  es.indices.create(index='questions', mappings=mappings)


def main() -> None:
    session: Session = SessionLocal()

    settings:Settings = get_settings()

    es_client = Elasticsearch(
            settings.ELASTIC_SERVER,
            #basic_auth=(settings.ELASTIC_USER, settings.ELASTIC_PASSWORD),
            verify_certs=False
    )
    try:
        
        delete_suppressed_faqs_from_elasticsearch(session, es_client)

        if not es_client.indices.exists(index='questions'):
            create_questions_index(es_client)

        questions = get_questionresponse_logs(
            session,         
            QuestionResponseLog.feedback == FeedbackStatus.REVISEE.value
        )

        processor = get_rag_processor()
        

        if questions:
            print(f'{INFO_LOG} fetching reviewed questions responses ...')

            for qrlog in questions:
                print(f'question : {qrlog.question} \nresponse :{qrlog.response}')
                schema_kwargs = {
                    "idBot": qrlog.idBot,
                    "question": qrlog.question,
                    "response": qrlog.response,
                    "langue": qrlog.langue,
                    "responseSource": qrlog.responseSource,
                    "feedback": qrlog.feedback,
                    "idLog": qrlog.idLog,
                    "question_embedding":processor.query_embedding(qrlog.question)
                }
                es_client.index(index="questions", document=schema_kwargs,id=str(qrlog.idLog))
            
            
            print(f'{INFO_LOG} marking reviewed feedbacks as treated ...')
            mark_reviewed_feedbacks_as_treated(session)

        else:
            print(f'{INFO_LOG} no reviewed questions responses found...')
    except Exception as e:
        print(f"{WAR_LOG} Critical error during execution: {str(e)}")

    finally:
        session.close()
        print(f"{INFO_LOG} Session closed.")

def delete_suppressed_faqs_from_elasticsearch(session: Session, es_client: Elasticsearch):
    suppressed_faqs = session.query(QuestionResponseLog).filter(
        QuestionResponseLog.feedback == FeedbackStatus.SUPPRIMEE.value
    ).all()

    if not suppressed_faqs:
        print(f"{INFO_LOG} No FAQs marked as deleted ({FeedbackStatus.SUPPRIMEE.value})")
        return

    for faq in suppressed_faqs:
        try:
            indexes = ["faq", "questions"]
            deleted_from_any_index = False

            for index in indexes:
                try:
                    es_client.get(index=index, id=str(faq.idLog))
                    es_client.delete(index=index, id=str(faq.idLog))
                    print(f"{INFO_LOG} Deleted FAQ {faq.idLog} from {index}")
                    deleted_from_any_index = True
                except Exception:
                    continue  # Document n'existe pas, on continue

            if deleted_from_any_index:
                session.delete(faq)
                session.commit()
                print(f"{INFO_LOG} Deleted FAQ {faq.idLog} from SQL database")

        except Exception as e:
            print(f"{WAR_LOG} Error processing FAQ {faq.idLog}: {str(e)}")
            session.rollback()


if __name__ == '__main__':
    main()
