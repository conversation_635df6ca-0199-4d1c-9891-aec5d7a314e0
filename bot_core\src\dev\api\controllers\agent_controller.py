from sqlalchemy.orm import Session
from src.dev.models.user_model import Agent
from src.dev.schemas.agent_sche import CreateAgentSchema, AgentUpdate, AgentSchema

# création d'agent
def create_agent(db: Session, agent_create: CreateAgentSchema) -> AgentSchema:
    db_agent = Agent(
        userName=agent_create.username,
        email=agent_create.email,
        password=agent_create.password,  
        role=agent_create.role,
        additionalInfo=agent_create.additional_info,
        is_assigned=agent_create.is_assigned,
        type="agent"
    )
    db.add(db_agent)
    db.commit()
    db.refresh(db_agent)
    return AgentSchema.from_orm(db_agent)

#liste des agent
def get_available_agents(db: Session, skip: int = 0, limit: int = 10) -> list[AgentSchema]:
    available_agents = (
        db.query(Agent)
        .filter(Agent.type == "agent", ~Agent.is_assigned)
        .offset(skip)
        .limit(limit)
        .all()
    )

    if available_agents:
        return [AgentSchema.from_orm(agent) for agent in available_agents]

    # Fallback: return all agents
    all_agents = (
        db.query(Agent)
        .filter(Agent.type == "agent")
        .offset(skip)
        .limit(limit)
        .all()
    )
    return [AgentSchema.from_orm(agent) for agent in all_agents]

# informations d'un agent
def get_agent(db: Session, agent_id: int) -> AgentSchema | None:
    agent = db.query(Agent).filter(Agent.userId == agent_id).first()
    if agent:
        return AgentSchema.from_orm(agent)
    return None

# modification d'un agent
def update_agent(db: Session, agent_id: int, agent_update: AgentUpdate) -> AgentSchema | None:
    agent = db.query(Agent).filter(Agent.userId == agent_id).first()
    if agent:
        if agent_update.email is not None:
            agent.email = agent_update.email
        if agent_update.role is not None:
            agent.role = agent_update.role
        if agent_update.is_assigned is not None:
            agent.is_assigned = agent_update.is_assigned

        db.commit()
        db.refresh(agent)
        return AgentSchema.from_orm(agent)
    return None

# suppression d'un agent
def delete_agent(db: Session, agent_id: int) -> bool:
    agent = db.query(Agent).filter(Agent.userId == agent_id).first()
    if agent:
        db.delete(agent)
        db.commit()
        return True
    return False
