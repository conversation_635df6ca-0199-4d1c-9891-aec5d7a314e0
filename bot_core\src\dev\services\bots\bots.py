

import json
import os

from fastapi import HTT<PERSON>Ex<PERSON>, status
from fastapi.responses import FileResponse
from sqlalchemy import select
from src.dev.config.config import Settings, get_settings
from src.dev.api import ClientController
from src.dev.exc import raise_with_log
from src.dev.models.bot_model import Bot
from src.dev.models.enums.user_type import UserType
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import User
from src.dev.schemas.auth import UserSchema
from src.dev.schemas.bot_config import BotConf
from src.dev.services.base import BaseDataManager, BaseService
from elasticsearch import Elasticsearch
from src.dev.schemas.faq_schema import FaqSchema

class BotServices(BaseService):

  def get_bot_conf_file(self, user: UserSchema, bot_id: int) -> FileResponse:
    bot_conf: BotConf = self.get_bot_conf(user, bot_id)
    user_id = user.user_id

    file_saved_path = self._save_bot_conf_file(bot_conf, user_id)

    if os.path.exists(file_saved_path):
      return FileResponse(path=file_saved_path, 
                          filename=os.path.basename(file_saved_path), 
                          media_type='application/octet-stream')
    else:
        raise HTTPException(status_code=404, detail="File not found.")


  def _save_bot_conf_file(self, bot_conf: BotConf, user_id: int) -> str:
    bot_id = bot_conf.botId
    folder_path = ClientController().get_client_folder_path(client_id=user_id, bot_id=bot_id)
    file_path = os.path.join(
      folder_path,
      f'bot_config.json'
    )

    try:
      with open(file_path, 'w') as config_file:
          json.dump(bot_conf.model_dump(), config_file, indent=4)
    except Exception as error:
      raise_with_log(status.HTTP_500_INTERNAL_SERVER_ERROR, error)
    
    return file_path


  def get_bot_conf(self, user: UserSchema, bot_id: int) -> BotConf:
    bot_model = BotDataManager(self.session).get_bot(bot_id)

    if user.type != UserType.admin:
      if not self.is_bot_owned_by_user(user, bot_id):
        raise_with_log(status.HTTP_401_UNAUTHORIZED, "Vous ne disposez pas des droits nécessaires pour generer un fichier de configuration pour ce bot")


    bot_conf = BotConf(botId=bot_model.idBot, 
                      botName=bot_model.botName, 
                      apiToken=bot_model.apiKey, 
                      applicationName= bot_model.applicationName)
      
    return bot_conf
  
  def is_bot_owned_by_user(self, user: UserSchema, bot_id: int) -> bool:
    bot_ids = BotDataManager(self.session).get_auth_user_bots_id(user.user_id)
    return bot_id in bot_ids

  def bot_faq(self, bot_id: int):
    settings:Settings = get_settings()

    es_client = Elasticsearch(
              settings.ELASTIC_SERVER,
    )
    query_string = {
            "term": {
                "idBot": bot_id 
            }
    }
    sort_string = {
                "frequency":{
                    "order": "desc"
                }
    } 

    results = es_client.search(
                index="faq", 
                query=query_string, 
                sort=sort_string,
                size=5,
                source_includes=["question", "response","frequency", "idBot"],
    )
    hits = results["hits"]["hits"]
    faq = [
            FaqSchema(
                question=hit["_source"]["question"],
                response=hit["_source"]["response"],
                idBot=hit["_source"]["idBot"],
                frequency=hit["_source"]["frequency"],
            )
            for hit in hits 
    ]  
    return faq 
    


class BotDataManager(BaseDataManager):
  
  def get_bot(self, bot_id: int) -> Bot:
    stm = select(Bot).where(Bot.idBot == bot_id)
    bot_model = self.get_one(stm)
    if bot_model is None: 
      raise_with_log(status.HTTP_404_NOT_FOUND, "bot non trouvé") 

    return bot_model
  
  def get_auth_user_bots(self, user_id: int):
    stmt = (
    select(Bot)  
    .join(Subscription, Subscription.idSubscription == Bot.idSubscription)
    .join(User, User.userId == Subscription.userId)
    .where(User.userId == user_id)
    )

    bots_model = self.get_all(stmt)
    if not bots_model:
        raise_with_log(status.HTTP_404_NOT_FOUND, "aucun bot trouve pour l'utilisateur connecter")

    return bots_model
  
  def get_auth_user_bots_id(self, user_id: int):
    bots = self.get_auth_user_bots(user_id)
    bot_ids = map(lambda bot: bot.idBot, bots)          
    return bot_ids