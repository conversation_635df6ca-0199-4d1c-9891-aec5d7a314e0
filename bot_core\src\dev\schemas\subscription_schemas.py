# schemas.py
from pydantic import BaseModel
from datetime import datetime
from pydantic import BaseModel, constr

class SouscriptionCreate(BaseModel):
    idPack: int
    userId: int
    startDate: datetime
    endDate: datetime
    status: constr(strip_whitespace=True) | int

    @classmethod
    def parse_obj(cls, obj):
        obj['startDate'] = datetime.strptime()
        obj['endDate'] = datetime.strptime()
        return super().parse_obj(obj)

class SouscriptionUpdate(BaseModel):
    idPack: int
    userId: int
    startDate: datetime
    endDate: datetime
    status: constr(strip_whitespace=True) | int

    @classmethod
    def parse_obj(cls, obj):
        obj['startDate'] = datetime.strptime()
        obj['endDate'] = datetime.strptime()
        return super().parse_obj(obj)
    
class SubscriptionSchema(BaseModel):
    idSubscription: int
    idPack: int
    userId: int
    nom_du_pack: str
    nom_de_utilisateur: str
    startDate: str
    endDate: str
    status: constr(strip_whitespace=True) | int


    class Config:
        from_attributes = True