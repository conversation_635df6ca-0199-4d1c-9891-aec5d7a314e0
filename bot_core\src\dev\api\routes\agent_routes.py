from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import <PERSON><PERSON><PERSON><PERSON>
from src.dev.utils.dependencies import get_db
from src.dev.api.controllers import agent_controller
from src.dev.schemas.agent_sche import CreateAgentSchema, AgentSchema, AgentUpdate

agents_router = APIRouter(
    prefix="/api/v1/agents",
    tags=["api_agent"]
)

agent_router = APIRouter(
    prefix="/api/v1/agent",
    tags=["api_agent"]
)

# Read all (available or fallback to all) agents
@agents_router.get("/", response_model=List[AgentSchema])
def read_agents(skip: int = 0, limit: int = 10, db: Session = Depends(get_db),
                user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.agent, UserType.admin]))):
    return agent_controller.get_available_agents(db, skip=skip, limit=limit)


# Read a specific agent
@agent_router.get("/{agent_id}", response_model=AgentSchema)
def read_agent(agent_id: int, db: Session = Depends(get_db),
               user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.agent, UserType.admin]))):
    agent = agent_controller.get_agent(db, agent_id)
    if not agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return agent

# Create a new agent
@agent_router.post("/", response_model=AgentSchema, status_code=status.HTTP_201_CREATED)
def create_agent(agent: CreateAgentSchema, db: Session = Depends(get_db),
                 user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.agent, UserType.admin]))):
    return agent_controller.create_agent(db, agent)



# Update an agent
@agent_router.put("/{agent_id}", response_model=AgentSchema)
def update_agent(agent_id: int, agent_update: AgentUpdate, db: Session = Depends(get_db),
                 user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.agent, UserType.admin]))):
    updated_agent = agent_controller.update_agent(db, agent_id, agent_update)
    if not updated_agent:
        raise HTTPException(status_code=404, detail="Agent not found")
    return updated_agent

# Delete an agent
@agent_router.delete("/{agent_id}", status_code=status.HTTP_200_OK)
def delete_agent(agent_id: int, db: Session = Depends(get_db),
                 user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.agent, UserType.admin]))):
    success = agent_controller.delete_agent(db, agent_id)
    if not success:
        raise HTTPException(status_code=404, detail="Agent not found")
    return {"detail": "Agent deleted successfully"}
