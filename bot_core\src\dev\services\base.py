from typing import (
    Any,
    List,
    Sequence,
    Type,
)
from sqlalchemy import (
    func,
    select,
)
from sqlalchemy.orm import Session

from src.dev.models.base import SQLModel


class SessionMixin:
    """Provides instance of database session."""

    def __init__(self, session: Session) -> None:
        self.session = session


class BaseService(SessionMixin):
    """Base class for application services."""

class BaseDataManager(SessionMixin):
    """Base data manager class responsible for operations over database."""

    def add_one(self, model: Any) -> None:
        self.session.add(model)
        self.session.commit()

    def add_all(self, models: Sequence[Any]) -> None:
        self.session.add_all(models)
        self.session.commit()


    def get_one(self, select_stmt) -> Any:
        return self.session.scalar(select_stmt)
        

    def get_all(self, select_stmt) -> List[Any]:
        return list(self.session.scalars(select_stmt).all())

    def get_from_tvf(self, model: Type[SQLModel], *args: Any) -> List[Any]:
        return self.get_all(self.select_from_tvf(model, *args))

    def delete_one(self, select_stmt):
        obj = self.get_one(select_stmt=select_stmt)
        self.session.delete(obj)
        self.session.commit()

    def update_one(self, update_stmt):
        self.session.execute(update_stmt)
        self.session.commit()
