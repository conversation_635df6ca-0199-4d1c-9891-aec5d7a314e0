import requests
#from config.config import OLLAMA_SERVER
from src.dev.config.config import get_settings ,Settings

app_settings: Settings = get_settings()

def call_ollama_api(question, model, rag_settings = None):
    url = app_settings.OLLAMA_SERVER + "/generate"
     
    rag_settings = rag_settings if isinstance(rag_settings, dict) else {}

    payload = {
        "model": model,
        "prompt": question,
        "stream": False,
        "options": rag_settings 
    }
    headers = {'Content-Type': 'application/json'}

    response = requests.post(url, json=payload, headers=headers)

    print(response)

    if response.status_code == 200:
        return response.json()['response']
    else:
        return f"Error: {response.text}"