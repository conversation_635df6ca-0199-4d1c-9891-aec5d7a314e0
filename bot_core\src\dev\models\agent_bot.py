from sqlalchemy import Column, <PERSON><PERSON>ger, ForeignKey
from src.dev.utils.database import Base
from sqlalchemy.orm import relationship

class AgentBot(Base):
    __tablename__ = 'agent_bot'

    agentUserId = Column(Integer, ForeignKey('agent.userId', ondelete="CASCADE"), primary_key=True)
    idBot = Column(Integer, ForeignKey('bot.idBot', ondelete="CASCADE"), primary_key=True)

    # Relation entre table 
    agent = relationship("Agent", back_populates="agent_bot")  