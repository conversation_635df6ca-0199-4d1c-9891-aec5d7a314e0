from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, APIRouter
from pydantic import BaseModel
from sqlalchemy import create_engine, <PERSON>umn, Integer, DateTime, ForeignKey, String
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session

from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import RoleChecker

DATABASE_URL = "postgresql://postgres:@localhost/chatbotdb_2025"

# Setup database
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class Subscription(Base):
    __tablename__ = "subscription"
    idSubscription = Column(Integer, primary_key=True, index=True, autoincrement=True)
    idPack = Column(Integer, ForeignKey('pack.idPack'))
    userId = Column(Integer, ForeignKey('user.userId'))
    startDate = Column(DateTime)
    endDate = Column(DateTime)
    status = Column(String)

    pack = relationship("Pack", back_populates="subscriptions")
    user = relationship("User", back_populates="subscriptions")

    @property
    def formatted_startDate(self):
        return self.startDate.strftime("%Y-%m-%d %H:%M:%S") if self.startDate else None

    @property
    def formatted_endDate(self):
        return self.endDate.strftime("%Y-%m-%d %H:%M:%S") if self.endDate else None

class Payment(Base):
    __tablename__ = "payment"
    paymentId = Column(Integer, primary_key=True, index=True, autoincrement=True)
    idSubscription = Column(Integer, ForeignKey('subscription.idSubscription'))

    subscription = relationship("Subscription", back_populates="payments")

Subscription.payments = relationship("Payment", back_populates="subscription", cascade="all, delete-orphan")

class User(Base):
    __tablename__ = "user"
    userId = Column(Integer, primary_key=True, index=True, autoincrement=True)
    userName = Column(String)

    subscriptions = relationship("Subscription", back_populates="user")

class Pack(Base):
    __tablename__ = "pack"
    idPack = Column(Integer, primary_key=True, index=True, autoincrement=True)
    packName = Column(String)

    subscriptions = relationship("Subscription", back_populates="pack")

Base.metadata.create_all(bind=engine)

router = APIRouter()

class SouscriptionCreate(BaseModel):
    idPack: int
    userId: int
    startDate: datetime
    endDate: datetime
    status: str

    @classmethod
    def parse_obj(cls, obj):
        obj['startDate'] = datetime.strptime(obj['startDate'], "%Y-%m-%d %H:%M:%S")
        obj['endDate'] = datetime.strptime(obj['endDate'], "%Y-%m-%d %H:%M:%S")
        return super().parse_obj(obj)

class SouscriptionUpdate(BaseModel):
    idPack: int
    userId: int
    startDate: datetime
    endDate: datetime
    status: str

    @classmethod
    def parse_obj(cls, obj):
        obj['startDate'] = datetime.strptime(obj['startDate'], "%Y-%m-%d %H:%M:%S")
        obj['endDate'] = datetime.strptime(obj['endDate'], "%Y-%m-%d %H:%M:%S")
        return super().parse_obj(obj)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.post("/souscriptions/")
def create_souscription(souscription: SouscriptionCreate, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_user = db.query(User).filter(User.userId == souscription.userId).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User n'existe pas")

    db_pack = db.query(Pack).filter(Pack.idPack == souscription.idPack).first()
    if db_pack is None:
        raise HTTPException(status_code=404, detail="Pack n'existe pas")

    db_souscription = Subscription(
        idPack=souscription.idPack,
        userId=souscription.userId,
        startDate=souscription.startDate.replace(microsecond=0),
        endDate=souscription.endDate.replace(microsecond=0),
        status=souscription.status,
    )
    try:
        db.add(db_souscription)
        db.commit()
        db.refresh(db_souscription)
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Souscription avec cet identifiant existe déjà")
    return {
        "idSubscription": db_souscription.idSubscription,
        "idPack": db_souscription.idPack,
        "userId": db_souscription.userId,
        "startDate": db_souscription.formatted_startDate,
        "endDate": db_souscription.formatted_endDate,
        "status": db_souscription.status,
        "nom_du_pack": db_souscription.pack.packName,  # get pack name
        "nom_de_utilisateur": db_souscription.user.userName,  # get user name
    }

@router.put("/souscriptions/{idSubscription}")
def update_souscription(idSubscription: int, souscription: SouscriptionUpdate, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscription = db.query(Subscription).filter(Subscription.idSubscription == idSubscription).first()
    if db_souscription is None:
        raise HTTPException(status_code=404, detail="Souscription not found")

    db_user = db.query(User).filter(User.userId == souscription.userId).first()
    if db_user is None:
        raise HTTPException(status_code=404, detail="User n'existe pas")

    db_pack = db.query(Pack).filter(Pack.idPack == souscription.idPack).first()
    if db_pack is None:
        raise HTTPException(status_code=404, detail="Pack n'existe pas")

    db_souscription.idPack = souscription.idPack
    db_souscription.userId = souscription.userId
    db_souscription.startDate = souscription.startDate.replace(microsecond=0)
    db_souscription.endDate = souscription.endDate.replace(microsecond=0)
    db_souscription.status = souscription.status
    db.commit()
    db.refresh(db_souscription)
    return {
        "idSubscription": db_souscription.idSubscription,
        "idPack": db_souscription.idPack,
        "userId": db_souscription.userId,
        "startDate": db_souscription.formatted_startDate,
        "endDate": db_souscription.formatted_endDate,
        "status": db_souscription.status,
        "nom_du_pack": db_souscription.pack.packName,  # get pack name
        "nom_de_utilisateur": db_souscription.user.userName,  # get user name
    }

@router.delete("/souscriptions/{idSubscription}")
def delete_souscription(idSubscription: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscription = db.query(Subscription).filter(Subscription.idSubscription == idSubscription).first()
    if db_souscription is None:
        raise HTTPException(status_code=404, detail="Souscription not found")
    try:
        db.delete(db_souscription)
        db.commit()
    except IntegrityError:
        db.rollback()
        raise HTTPException(status_code=400, detail="Erreur de suppression: La souscription est encore référencée")
    return {"detail": "Souscription deleted"}

@router.get("/souscriptions/")
def get_souscriptions(db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscriptions = db.query(Subscription).all()
    return [
        {
            "idSubscription": souscription.idSubscription,
            "idPack": souscription.idPack,
            "userId": souscription.userId,
            "startDate": souscription.formatted_startDate,
            "endDate": souscription.formatted_endDate,
            "status": souscription.status,
            "nom_du_pack": souscription.pack.packName,  # get pack name
            "nom_de_utilisateur": souscription.user.userName,  # get user name
        }
        for souscription in db_souscriptions
    ]

app = FastAPI()
app.include_router(router)
