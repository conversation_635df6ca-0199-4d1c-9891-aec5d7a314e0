# from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# from src.dev.utils.database import Base
# from sqlalchemy.orm import relationship

# class AgentBot(Base):
#     __tablename__ = 'agent_bot'

#     agentUserId = Column(Integer, Foreign<PERSON>ey('agent.userId', ondelete="CASCADE"), primary_key=True)
#     idBot = Column(Integer, ForeignKey('bot.idBot', ondelete="CASCADE"), primary_key=True)

#     # Relation entre table 
#     agent = relationship("Agent", back_populates="agent_bots")  

    
