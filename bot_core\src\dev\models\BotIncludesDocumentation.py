from sqlalchemy import <PERSON><PERSON>n, Integer, Foreign<PERSON>ey, PrimaryKeyConstraint
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base

class BotIncludeDocumentation(Base):
    __tablename__ = 'botincludedocumentation'

    idBot = Column(Integer, ForeignKey('bot.idBot'), primary_key=True, nullable=False)
    idDocumentation = Column(Integer, ForeignKey('documentation.idDocumentation'), primary_key=True, nullable=False)

    __table_args__ = (
        PrimaryKeyConstraint('idBot', 'idDocumentation'),
    )

    # Relationships
    bot = relationship("Bot", back_populates="bot_includes")
    documentation = relationship("Documentation", back_populates="bot_includes")
