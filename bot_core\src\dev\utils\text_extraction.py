# text_extraction.py inside /utils directory

import pdfplumber
from bs4 import BeautifulSoup
import tiktoken

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file."""
    text = ""
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text += page.extract_text() or ""  # Concatenate text of each page
    return text

def extract_text_from_html(html_path):
    """Extract text from an HTML file."""
    with open(html_path, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')
        return soup.get_text()

# You can also add other utility functions as needed

# TOFIX: use the appropriate model name 
def get_token_count(text: str):
    encoding = tiktoken.get_encoding('cl100k_base')
    num_tokens = len(encoding.encode(text))
    return num_tokens