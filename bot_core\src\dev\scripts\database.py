import os
import click
import shutil
from random import randint, choice
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text, inspect
from src.dev.const import ERROR_LOG, INFO_LOG, WAR_LOG
from src.dev.scripts.process_docs import process_all_unprocessed
from src.dev.api import ClientController
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.enums.user_type import UserType
from src.dev.models.enums.DocumentTypeEnum import DocumentTypeEnum
from src.dev.schemas.admin_schema import CreateAdminSchema
from src.dev.schemas.client_schema import CreateClientSchema
from src.dev.schemas.visitor_schema import CreateVisitorSchema
from src.dev.services.auth.auth import AuthService
from src.dev.services.rag.rag_processor_factory import get_rag_processor
from src.dev.utils.database import Base, SessionLocal, engine
from src.dev.models.bot_model import Bot
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
from src.dev.models.question_response_model import QuestionResponseLog, PriorityEnum, ResponseSourceEnum, FeedbackStatus
from src.dev.models.documentation_model import Documentation
from src.dev.models.pack_model import Pack
from src.dev.models.payment_model import Payment
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import User
from src.dev.models.token_usage_model import TokenUsageLog

from src.dev.schemas.agent_sche import CreateAgentSchema
from src.dev.models.agent_bot import AgentBot
import enum, random
import psycopg2
from src.dev.config.config import get_settings , Settings
from sqlalchemy import text
from src.dev.utils.database import engine  # ou importer ton engine globalement
from sqlalchemy import create_engine,text

@click.group
def mycommands():
    pass


def check_and_create_enum_type():
    """Vérifier et créer le type ENUM agent_role s'il n'existe pas"""
    session = get_session()
    try:
        # Vérifier si le type ENUM existe
        result = session.execute(text(
            "SELECT 1 FROM pg_type WHERE typname = 'agent_role'"
        )).fetchone()
        
        if not result:
            print(f'{INFO_LOG} Création du type ENUM agent_role...')
            session.execute(text(
                "CREATE TYPE agent_role AS ENUM ('admin', 'agent', 'supervisor', 'bot')"
            ))
            session.commit()
            print(f'{INFO_LOG} Type ENUM agent_role créé avec succès.')
    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de la création du type ENUM: {e}')
        session.rollback()
    finally:
        session.close()


def create_agent_tables():
    """Créer les tables agent et agent_bot si elles n'existent pas"""
    session = get_session()
    try:
        # Créer la table agent
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS public.agent (
                "userId" integer NOT NULL,
                role agent_role NOT NULL,
                "isAssigned" boolean NOT NULL DEFAULT false,
                CONSTRAINT agent_pkey PRIMARY KEY ("userId"),
                CONSTRAINT fk_user FOREIGN KEY ("userId")
                    REFERENCES public."user" ("userId") MATCH SIMPLE
                    ON UPDATE NO ACTION
                    ON DELETE CASCADE
            )
        """))
        
        # Créer l'index
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS ix_agent_isassigned
                ON public.agent ("isAssigned" ASC NULLS LAST)
        """))
        
        # Créer la table agent_bot
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS public.agent_bot (
                "agentUserId" integer NOT NULL,
                "idBot" integer NOT NULL,
                CONSTRAINT agent_bot_pkey PRIMARY KEY ("agentUserId", "idBot"),
                CONSTRAINT fk_agent FOREIGN KEY ("agentUserId")
                    REFERENCES public.agent ("userId") MATCH SIMPLE
                    ON UPDATE NO ACTION
                    ON DELETE CASCADE,
                CONSTRAINT fk_bot FOREIGN KEY ("idBot")
                    REFERENCES public.bot ("idBot") MATCH SIMPLE
                    ON UPDATE NO ACTION
                    ON DELETE CASCADE
            )
        """))
        
        session.commit()
        print(f'{INFO_LOG} Tables agent et agent_bot créées avec succès.')
    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de la création des tables agent: {e}')
        session.rollback()
    finally:
        session.close()


def add_question_response_columns():
    """Ajouter les nouvelles colonnes à la table questionreponselog"""
    session = get_session()
    try:
        inspector = inspect(engine)
        columns = [col['name'] for col in inspector.get_columns('questionreponselog')]
        
        # Ajouter la colonne priority si elle n'existe pas
        if 'priority' not in columns:
            print(f'{INFO_LOG} Ajout de la colonne priority...')
            session.execute(text(
                'ALTER TABLE public.questionreponselog ADD COLUMN priority character varying(10)'
            ))
        
        # Ajouter la colonne responseSource si elle n'existe pas
        if 'responseSource' not in columns:
            print(f'{INFO_LOG} Ajout de la colonne responseSource...')
            session.execute(text(
                'ALTER TABLE public.questionreponselog ADD COLUMN "responseSource" character varying(20)'
            ))
        
        # Ajouter la colonne agentUserId si elle n'existe pas
        if 'agentUserId' not in columns:
            print(f'{INFO_LOG} Ajout de la colonne agentUserId...')
            session.execute(text(
                'ALTER TABLE public.questionreponselog ADD COLUMN "agentUserId" integer'
            ))
        
        session.commit()
        print(f'{INFO_LOG} Colonnes ajoutées avec succès.')
    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de l\'ajout des colonnes: {e}')
        session.rollback()
    finally:
        session.close()


def add_question_response_constraints():
    """Ajouter les contraintes à la table questionreponselog"""
    session = get_session()
    try:
        # Récupérer les contraintes existantes
        existing_constraints = session.execute(text("""
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name = 'questionreponselog' AND constraint_type = 'CHECK'
        """)).fetchall()
        
        constraint_names = [row[0] for row in existing_constraints]
        
        # Ajouter la contrainte priority_check si elle n'existe pas
        if 'questionreponselog_priority_check' not in constraint_names:
            print(f'{INFO_LOG} Ajout de la contrainte priority_check...')
            session.execute(text("""
                ALTER TABLE public.questionreponselog 
                ADD CONSTRAINT questionreponselog_priority_check 
                CHECK (priority IN ('low', 'medium', 'high'))
            """))
        
        # Ajouter la contrainte responseSource_check si elle n'existe pas
        if 'questionreponselog_responsesource_check' not in constraint_names:
            print(f'{INFO_LOG} Ajout de la contrainte responseSource_check...')
            session.execute(text("""
                ALTER TABLE public.questionreponselog 
                ADD CONSTRAINT questionreponselog_responsesource_check 
                CHECK ("responseSource" IN ('agent', 'ia', 'elastic'))
            """))
        
        # Vérifier les contraintes de clé étrangère
        fk_constraints = session.execute(text("""
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name = 'questionreponselog' AND constraint_type = 'FOREIGN KEY'
        """)).fetchall()
        
        fk_names = [row[0] for row in fk_constraints]
        
        # Ajouter la contrainte de clé étrangère pour agentUserId si elle n'existe pas
        if 'fk_question_agent' not in fk_names:
            print(f'{INFO_LOG} Ajout de la contrainte de clé étrangère fk_question_agent...')
            session.execute(text("""
                ALTER TABLE public.questionreponselog 
                ADD CONSTRAINT fk_question_agent 
                FOREIGN KEY ("agentUserId") 
                REFERENCES public.agent("userId") 
                ON UPDATE NO ACTION 
                ON DELETE SET NULL
            """))
        
        session.commit()
        print(f'{INFO_LOG} Contraintes ajoutées avec succès.')
    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de l\'ajout des contraintes: {e}')
        session.rollback()
    finally:
        session.close()


def create_database_if_not_exists():
    """Créer la base de données si elle n'existe pas (utilise les variables .env directement)."""


   
    settings:Settings = get_settings()

    db_user = settings.DATABASE_USER
    db_password = settings.DATABASE_PASSWORD 
    db_host = settings.DATABASE_HOST 
    db_port = settings.DATABASE_PORT 
    db_name = settings.DATABASE_NAME 

    if not db_name:
        raise ValueError("DATABASE_NAME is not defined in environment variables")

    try:
        # Connexion à la base postgres (template par défaut)
        conn = psycopg2.connect(
            dbname=db_name,
            user=db_user,
            password=db_password,
            host=db_host,
            port=db_port
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # Vérifie si la base existe
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone()

        if not exists:
            print(f"{INFO_LOG} Base '{db_name}' non trouvée. Création en cours...")
            cursor.execute(f'CREATE DATABASE "{db_name}"')
            print(f"{INFO_LOG} Base '{db_name}' créée avec succès.")
        else:
            print(f"{INFO_LOG} La base '{db_name}' existe déjà.")

        cursor.close()
        conn.close()
    except Exception as e:
        print(f"{ERROR_LOG} Erreur lors de la création de la base : {e}")
        raise

def enable_pgvector_extension():
    """Active l'extension pgvector si elle n'existe pas."""
        
    settings = get_settings()
    print(f'{INFO_LOG} DATABASE_URL : {settings.DATABASE_URL}')

    # Recrée un engine pointant sur la bonne base (chatbotdb_2025)
    my_db_engine = create_engine(settings.DATABASE_URL)
 
    with my_db_engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
        conn.commit() 
        print(f'{INFO_LOG} Extension pgvector activée (ou déjà existante).')



@click.command()
def rebuild_all_tables():
    """Reconstruire toutes les tables avec les nouvelles modifications"""
    session: Session = get_session()
    
    create_database_if_not_exists()

    enable_pgvector_extension()

    print(f'{INFO_LOG} Début de la reconstruction des tables...')
    
    # Supprimer toutes les tables existantes
    drop_everything(session)
    
    # Créer le type ENUM agent_role
    # check_and_create_enum_type()
    
    # Créer toutes les tables de base avec SQLAlchemy
    create_all_tables()
    
    # Créer les tables agent spécifiques
    # create_agent_tables()
    
    # Ajouter les nouvelles colonnes à questionreponselog
    # add_question_response_columns()
    
    # Ajouter les contraintes
    # add_question_response_constraints()
    
    print(f'{INFO_LOG} La reconstruction de toutes les tables est terminée.')
    print(f'{INFO_LOG} Vous pouvez trouver les déclencheurs dans : docubot/Database/triggers.sql')
    print(f'{WAR_LOG} Assurez-vous d\'exécuter tous vos déclencheurs avant d\'ajouter de nouvelles données.')
    print(f'{WAR_LOG} N\'exécutez pas tout le fichier en une seule fois; exécutez-le function par function.')


@click.command()
def generate_db_data():
    """Générer des données de test"""
    print(f'{INFO_LOG} Génération des données de test...')
    
    # Créer les utilisateurs de base
    for i in range(3):
        add_admin(f'admin {i+1}', f'admin{i+1}@gmail.com', f'admin{i+1}2024', 'compte admin par default')

    for i in range(3):
        add_client(f'client {i+1}', f'client{i+1}@gmail.com', f'client{i+1}2024', 'compte client par default', randint(1, 3), f'company {i+1}')
    
    for i in range(20):   
        add_visitor(f'visitor {i+1}', f'visitor{i+1}@gmail.com', f'visitor{i+1}2024', 'compte visitor par default')
    
    # Créer les données de base
    add_packs()   
    add_subscriptions()
    add_payments()
    add_bots()
    
    # Créer les agents et leurs liaisons
    add_agents_and_bots()
    
    # Uploader un document de test
    mock_upload_doc('lsagency_userguide.pdf')
    
    # Ajouter des questions avec les nouveaux champs
    add_questions()
    
    print(f'{INFO_LOG} Génération des données terminée.')


def add_agents_and_bots():
    """Ajouter des agents et créer des liaisons avec les bots"""
    session = get_session()
    
    try:
        # Créer des agents de test
        agents_data = [
            {'userId': 4, 'role': 'admin', 'isAssigned': True},
            {'userId': 5, 'role': 'agent', 'isAssigned': True},
            {'userId': 6, 'role': 'supervisor', 'isAssigned': False},
            {'userId': 7, 'role': 'agent', 'isAssigned': True},
            {'userId': 8, 'role': 'supervisor', 'isAssigned': False},
            {'userId': 9, 'role': 'bot', 'isAssigned': True},
        ]
        
        for agent_data in agents_data:
            # Vérifier si l'agent existe déjà
            existing_agent = session.execute(text(
                'SELECT 1 FROM agent WHERE "userId" = :user_id'
            ), {'user_id': agent_data['userId']}).fetchone()
            
            if not existing_agent:
                session.execute(text("""
                    INSERT INTO agent ("userId", role, "isAssigned") 
                    VALUES (:user_id, :role, :is_assigned)
                """), {
                    'user_id': agent_data['userId'],
                    'role': agent_data['role'],
                    'is_assigned': agent_data['isAssigned']
                })
        
        # Créer des liaisons agent-bot
        agent_bot_data = [
            {'agentUserId': 4, 'idBot': 1},
            {'agentUserId': 4, 'idBot': 2},
            {'agentUserId': 5, 'idBot': 1},
            {'agentUserId': 6, 'idBot': 2},
            {'agentUserId': 7, 'idBot': 3},
            {'agentUserId': 8, 'idBot': 4},
            {'agentUserId': 9, 'idBot': 5},
        ]
        
        for link_data in agent_bot_data:
            # Vérifier si la liaison existe déjà
            existing_link = session.execute(text(
                'SELECT 1 FROM agent_bot WHERE "agentUserId" = :agent_id AND "idBot" = :bot_id'
            ), {
                'agent_id': link_data['agentUserId'],
                'bot_id': link_data['idBot']
            }).fetchone()
            
            if not existing_link:
                session.execute(text("""
                    INSERT INTO agent_bot ("agentUserId", "idBot") 
                    VALUES (:agent_id, :bot_id)
                """), {
                    'agent_id': link_data['agentUserId'],
                    'bot_id': link_data['idBot']
                })
        
        session.commit()
        print(f'{INFO_LOG} Agents et liaisons créés avec succès.')
        
    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de la création des agents: {e}')
        session.rollback()
    finally:
        session.close()


def add_admin(username, email, password, add_info):
    session: Session = get_session()
    new_admin = CreateAdminSchema(
        username=username,
        email=email,
        password=password,
        additional_info=add_info
    )
    AuthService(session).create_account(new_admin)
    session.commit()


def add_client(username, email, password, add_info, admin_id, company_name):
    session: Session = get_session()
    new_client = CreateClientSchema(
        username=username,
        email=email,
        password=password,
        additional_info=add_info,
        adminUserId=admin_id,
        companyName=company_name
    )
    AuthService(session).create_account(new_client)
    session.commit()


def add_visitor(username, email, password, add_info):
    session: Session = get_session()
    new_visitor = CreateVisitorSchema(
        username=username,
        email=email,
        password=password,
        additional_info=add_info
    )
    AuthService(session).create_account(new_visitor)
    session.commit()


def add_packs():
    session: Session = get_session()
    packs = [
        {'packName': 'Starter', 'maxOutputToken': 5000, 'maxInputToken': 5000, 'price': 9.99},
        {'packName': 'Basic', 'maxOutputToken': 100000, 'maxInputToken': 100000, 'price': 19.99},
        {'packName': 'Pro', 'maxOutputToken': 1000000, 'maxInputToken': 1000000, 'price': 49.99},
        {'packName': 'Premium', 'maxOutputToken': ********, 'maxInputToken': ********, 'price': 99.99},
        {'packName': 'Entreprise', 'maxOutputToken': **********, 'maxInputToken': **********, 'price': 199.99},
    ]
    for pack in packs:   
        new_pack = Pack(
            packName=pack.get('packName'),
            maxOutputToken=pack.get('maxOutputToken'),
            maxInputToken=pack.get('maxInputToken'),
            price=pack.get('price'),
        ) 
        session.add(new_pack)
    session.commit()
  

def add_subscriptions():
    session: Session = get_session()
    client_ids = session.query(User.userId).where(User.type == UserType.client).all()
    client_ids = list(zip(*client_ids))[0]

    for i in range(5):
        start, end = generate_subscription_dates()

        new_subscription = Subscription(
            idPack=randint(1, 5),
            userId=choice(client_ids),
            startDate=start,
            endDate=end,
            status=1
        )
        session.add(new_subscription)
    session.commit()


def add_payments():
    session: Session = get_session()
    client_ids = session.query(User.userId).where(User.type == UserType.client).all()
    client_ids = list(zip(*client_ids))[0]
    
    sub_ids = session.query(Subscription.idSubscription).all()
    sub_ids = list(zip(*sub_ids))[0]

    payment_methods = ['Credit Card', 'PayPal', 'Bank Transfer']
    payment_status = ['pending', 'completed', 'failed', 'refunded']
    amounts = [9.99, 19.99, 49.99, 99.99, 199.99]
    for sub_id in sub_ids:
        date, _ = generate_subscription_dates()
        new_payments = Payment(
            userId=choice(client_ids),
            idSubscription=sub_id,
            amount=choice(amounts),
            paymentMethod=choice(payment_methods),
            paymentStatus=choice(payment_status),
            timestamp=date,
        )
        session.add(new_payments)
    session.commit()


def add_bots():
    session: Session = get_session()
    client_ids = session.query(User.userId).where(User.type == UserType.client).all()
    client_ids = list(zip(*client_ids))[0]
    
    sub_ids = session.query(Subscription.idSubscription).all()
    sub_ids = list(zip(*sub_ids))[0]

    bot_names = [
        "Locasmart Service clients",
        "Bot RH",
        "Bot Support",
        "Bot Finance",
        "Bot Documentation",
        "Bot Formation",
        "Bot Service Public",
        "Bot Accessibilité",
        "Bot Multilingue",
        "Bot Qualité de Vie",
        "Bot Juridique",
        "Bot Naturalisation",
        "Bot Citoyenneté",
        "Bot Ministère Intérieur",
        "Bot Éducation",
        "Bot Inscriptions",
        "Bot Recrutement",
        "Bot Démarches Sociales",
        "Bot Santé Travail",
        "Bot Facturation",
        "Mind Mate",
        "Bot Mobilité Interne"
    ]

    application_names = [
        "LocaSmart",            # RH
        "PeopleCare",            # RH
        "HelpFlow",              # Support & IT
        "FinBot",                # Finance
        "DocsNavigator",         # Accès documentaire
        "EduBot",                # Formation interne
        "CivicAssistant",        # Collectivités
        "InclusivBot",           # Accessibilité
        "LanguAI",               # Multilingue
        "MoodScan",              # QVT & entretiens
        "LegalEase",             # Juridique / contrats
        "NaturalBot",            # Aide à la naturalisation
        "CitizenFlow",           # Démarches citoyennes
        "SecureAdmin",           # Ministère intérieur
        "SchoolConnect",         # Ministère Éducation
        "Inscripto",             # Gestion des inscriptions
        "TalentTrack",           # Recrutement & onboarding
        "SocialAccess",          # Aide CAF, RSA, logement…
        "WorkHealth",            # Médecine du travail
        "EasyInvoice",           # Gestion des factures
        "MindMate",              # Appli de psycho
        "MobilityHub"           # Mutations & mobilité interne
    ]

    for i in range(min(len(bot_names), len(application_names))):
        new_bot = Bot(
            idSubscription=choice(sub_ids), 
            botName=bot_names[i], 
            applicationName=application_names[i], 
            currentInputToken=0, 
            currentOutputToken=0, 
            isLimitReached=0
        )
        session.add(new_bot)
    session.commit()


def mock_upload_doc(doc_name):
    session: Session = get_session()

    try:
        file_path = os.path.abspath(f'../docs/{doc_name}')
    except:
        print(f"{ERROR_LOG} '{doc_name}' not found in docubot/docs/")

    save_path = ClientController().get_client_folder_path(5, 1)
    save_path = os.path.join(save_path, doc_name)
    shutil.copyfile(file_path, save_path)

    document = Documentation(
        title=doc_name, 
        url=save_path, 
        type=DocumentTypeEnum.TYPE_DOC_PDF.value,
        embedded=False
    )
    session.add(document) 
    session.commit()
    session.refresh(document)

    bot_include_doc = BotIncludeDocumentation(
        idBot=1,
        idDocumentation=document.idDocumentation
    )
    session.add(bot_include_doc)
    session.commit()
    session.refresh(bot_include_doc)

def generate_subscription_dates():
    current_date = datetime.now()
    start_date = current_date - timedelta(days=randint(0, 365))
    end_date = current_date + timedelta(days=randint(0, 730))
    
    if end_date <= start_date:
        end_date = start_date + timedelta(days=randint(1, 730))
    
    start_date_str = start_date.strftime("%Y-%m-%d %H:%M:%S")
    end_date_str = end_date.strftime("%Y-%m-%d %H:%M:%S")
    
    return start_date_str, end_date_str


def get_session() -> Session:
    return SessionLocal()


def create_all_tables():
    Base.metadata.create_all(bind=engine)


def drop_everything(session):
    from sqlalchemy import inspect
    from sqlalchemy.schema import (
        DropConstraint,
        DropTable,
        MetaData,
        Table,
        ForeignKeyConstraint,
    )

    con = engine.connect()
    trans = con.begin()
    inspector = inspect(engine)

    meta = MetaData()
    tables = []
    all_fkeys = []

    for table_name in inspector.get_table_names():
        fkeys = []

        for fkey in inspector.get_foreign_keys(table_name):
            if not fkey["name"]:
                continue

            fkeys.append(ForeignKeyConstraint((), (), name=fkey["name"]))

        tables.append(Table(table_name, meta, *fkeys))
        all_fkeys.extend(fkeys)

    for fkey in all_fkeys:
        con.execute(DropConstraint(fkey))

    for table in tables:
        con.execute(DropTable(table))

    trans.commit()


@click.command()
def embbed_doc():
    session: Session = get_session()
    
    try:
        process_all_unprocessed(session)
    finally:
        session.close()

 

def add_questions():
    """Ajouter des questions avec les nouveaux champs priority, responseSource et agentUserId"""
    session: Session = get_session()
    try:
        # Get existing agent user IDs from the database
        existing_agent_ids = session.execute(text('SELECT "userId" FROM agent')).fetchall()
        agent_user_ids = [row[0] for row in existing_agent_ids] if existing_agent_ids else []

        # Add None for IA responses
        agent_user_ids.append(None)

        print(f'{INFO_LOG} Agents disponibles: {agent_user_ids}')


        qa = [
            ("Qu'est-ce que le menu pour ajouter des véhicules ?", "Le menu pour ajouter des véhicules est 'Edition véhicules' dans le menu 'Parc & Accessoires'. Il permet d'ajouter de nouveaux véhicules au système, de modifier les informations des véhicules existants ou de les supprimer de l'inventaire."),
            # Variations
            ("Quel est le menu pour ajouter des véhicules ?", "Le menu pour ajouter des véhicules est 'Edition véhicules' dans le menu 'Parc & Accessoires'."),
            ("Comment ajouter des véhicules via le menu ?", "Le menu 'Edition véhicules' dans le menu 'Parc & Accessoires' permet d'ajouter de nouveaux véhicules."),
            ("Ajouter des véhicules : quel menu utiliser ?", "Le menu 'Edition véhicules' dans le menu 'Parc & Accessoires' permet d'ajouter, modifier ou supprimer des véhicules."),
            ("Où se trouve le menu pour ajouter des véhicules ?", "Le menu 'Edition véhicules' se trouve dans le menu 'Parc & Accessoires'."),
            ("où se trouve le menu pour ajouter des véhicules?", "Le menu 'Edition véhicules' est situé dans le menu 'Parc & Accessoires'."),
            ("le menu pour ajouter des véhicules c'est quoi ?", "Il s'agit du menu 'Edition véhicules' dans le menu 'Parc & Accessoires'."),
            ("Menu pour ajouter des véhicules ?", "Le menu 'Edition véhicules' dans 'Parc & Accessoires'."),
            ("Ajout de véhicules : quel menu ?", "Utilisez le menu 'Edition véhicules' dans 'Parc & Accessoires'."),
            ("Dans quel menu peut-on ajouter des véhicules ?", "Le menu 'Edition véhicules' dans 'Parc & Accessoires' le permet."),
            ("Ajout de véhicules - quel est le menu ?", "C'est le menu 'Edition véhicules' dans 'Parc & Accessoires'."),

            ("Comment fonctionne la fonctionnalité de recherche rapide ?", "La fonctionnalité de recherche rapide permet aux utilisateurs de rechercher rapidement des véhicules, des réservations, des clients, ou toute autre information importante dans l'application, en tapant les mots-clés pertinents."),
            # Variations
            ("Fonctionnalité de recherche rapide : comment ça marche ?", "La fonctionnalité de recherche rapide permet de trouver rapidement des informations clés dans l'application."),
            ("La recherche rapide, comment ça marche ?", "Elle permet de rechercher des véhicules, clients, réservations, etc., par mots-clés."),
            ("Qu'est-ce que la recherche rapide ?", "La recherche rapide aide à trouver rapidement des informations dans l'application."),
            ("Recherche rapide - explication ?", "Elle permet de retrouver des données importantes en tapant simplement les mots-clés."),
            ("comment fonctionne la recherche rapide ?", "La recherche rapide permet de trouver rapidement des informations essentielles."),
            ("La fonctionnalité de recherche rapide : quel usage ?", "Elle est utilisée pour rechercher des informations clés en tapant des mots-clés."),
            ("Utilisation de la recherche rapide ?", "Elle facilite la recherche rapide de véhicules, clients, et plus."),
            ("Pourquoi utiliser la recherche rapide ?", "Pour obtenir rapidement des résultats pertinents en fonction des mots-clés saisis."),
            ("La recherche rapide : comment l'utiliser ?", "Il suffit de saisir les mots-clés pour obtenir des résultats instantanés."),

            ("Que contient le menu Tableau de Bord ?", "Le menu Tableau de Bord donne une vue d'ensemble de l'activité, y compris les réservations à venir, les véhicules actuellement loués, et les alertes importantes. Il contient également des indicateurs clés, des informations sur la gestion des documents et la maintenance, la gestion financière, et des analyses et statistiques."),
            # Variations
            ("Que trouve-t-on dans le menu Tableau de Bord ?", "On y trouve un résumé des activités, des réservations, des alertes, etc."),
            ("Menu Tableau de Bord : contenu ?", "Il offre une vue complète sur les réservations, les véhicules loués, et plus encore."),
            ("Quelles informations sont dans le Tableau de Bord ?", "On y retrouve les réservations, les indicateurs clés, et des statistiques."),
            ("Le menu Tableau de Bord - qu'est-ce qu'il propose ?", "Il offre une vue d'ensemble des activités et des indicateurs."),
            ("Qu'inclut le Tableau de Bord ?", "Il inclut les réservations à venir, les véhicules loués, et plus."),
            ("Quelles données sont accessibles dans le Tableau de Bord ?", "Les données incluent les alertes, les indicateurs, et les statistiques."),
            ("Quelles sections sont présentes dans le Tableau de Bord ?", "Le Tableau de Bord présente l'activité globale, les véhicules, et les réservations."),
            ("Tableau de Bord - quelles informations y accéder ?", "On peut y accéder aux informations financières, aux alertes, et aux analyses."),

            ("Comment ajouter un nouveau véhicule au système ?", "Pour ajouter un nouveau véhicule, accédez au menu 'Édition Véhicules', cliquez sur 'Ajout-Modification de véhicule', remplissez le formulaire avec les détails du véhicule, les documents administratifs, et les informations d'achat, puis cliquez sur 'Enregistrer'."),
            # Variations
            ("Procédure pour ajouter un véhicule ?", "Accédez à 'Édition Véhicules', remplissez le formulaire et enregistrez."),
            ("Ajouter un véhicule : quelles étapes ?", "Utilisez le menu 'Édition Véhicules' et suivez les étapes pour enregistrer le véhicule."),
            ("Comment enregistrer un nouveau véhicule ?", "Passez par le menu 'Édition Véhicules', remplissez le formulaire et cliquez sur 'Enregistrer'."),
            ("Où ajouter un véhicule dans le système ?", "Dans 'Édition Véhicules', suivez les étapes pour l'ajout."),
            ("Ajout d'un véhicule - guide ?", "Allez dans 'Édition Véhicules', complétez le formulaire, et enregistrez."),
            ("étapes pour ajouter un véhicule ?", "Dans 'Édition Véhicules', remplissez les informations requises et cliquez sur 'Enregistrer'."),

            ("Quels détails peuvent être modifiés dans le menu Edition véhicules ?", "Dans le menu Edition véhicules, on peut modifier des détails tels que la marque, le modèle, le numéro de plaque, l'état du véhicule, et d'autres informations spécifiques."),
            # Variations
            ("Quelles informations peuvent être changées dans Edition véhicules ?", "Les détails comme la marque, le modèle, et le numéro de plaque peuvent être modifiés."),
            ("Quels éléments sont modifiables dans le menu Edition véhicules ?", "On peut modifier la marque, le modèle, l'état, etc."),
            ("Que peut-on éditer dans Edition véhicules ?", "Des éléments comme le numéro de plaque et l'état peuvent être édités."),
            ("Dans le menu Edition véhicules, que peut-on changer ?", "Il est possible de changer la marque, le modèle, et d'autres détails."),

            ("Comment suivre l'entretien des véhicules dans le système ?", "L'entretien des véhicules peut être suivi dans le menu 'Cahier d'entretien', qui fournit un système pour suivre l'entretien et les réparations effectuées sur chaque véhicule, incluant les services réguliers, les réparations imprévues, les coûts associés, et les dates des interventions."),
            # Variations
            ("Suivi de l'entretien des véhicules : comment faire ?", "Le menu 'Cahier d'entretien' permet de suivre les entretiens et les réparations."),
            ("Où suivre l'entretien des véhicules ?", "Le suivi se fait dans le menu 'Cahier d'entretien' pour chaque véhicule."),
            ("Quel menu pour suivre l'entretien des véhicules ?", "Le suivi se fait via le menu 'Cahier d'entretien'."),
            ("Comment gérer l'entretien des véhicules ?", "Utilisez 'Cahier d'entretien' pour suivre les services réguliers et réparations."),

            ("Que montre le menu Véhicules en service ?", "Le menu Véhicules en service offre une vue d'ensemble des véhicules actuellement en service et disponibles pour la location. Il permet de suivre l'utilisation des véhicules et de planifier leur disponibilité."),
            # Variations
            ("Qu'affiche le menu Véhicules en service ?", "Il affiche une vue des véhicules en service et disponibles pour la location."),
            ("Quels véhicules sont visibles dans Véhicules en service ?", "Ce sont les véhicules actuellement en service et prêts à être loués."),
            ("Véhicules en service - quelles informations y voir ?", "Les véhicules en service et leur disponibilité pour la location."),
            ("Qu'inclut le menu Véhicules en service ?", "Il inclut les véhicules en service, disponibles pour la location."),

            ("Comment gérer les véhicules hors service ?", "Les véhicules hors service sont gérés dans le menu 'Véhicules hors service', qui liste les véhicules qui ne sont actuellement pas disponibles pour la location en raison de réparations, d'entretien, ou d'autres raisons."),
            # Variations
            ("Quel menu pour gérer les véhicules hors service ?", "Les véhicules hors service se gèrent dans le menu 'Véhicules hors service'."),
            ("Comment voir les véhicules hors service ?", "Ils sont listés dans le menu 'Véhicules hors service'."),
            ("Véhicules hors service - où les gérer ?", "Le menu 'Véhicules hors service' permet de les gérer."),
            ("Gérer les véhicules hors service : comment faire ?", "Passez par le menu 'Véhicules hors service'."),

            ("Comment gérer les véhicules de partenaires ?", "Les véhicules de partenaires sont gérés dans le menu 'Véhicules de partenaires', qui permet de gérer les véhicules appartenant à des partenaires mais disponibles pour la location à travers votre service."),

            ("Quels documents des véhicules peuvent être gérés dans le menu Documents des véhicules ?", "Le menu Documents des véhicules permet de gérer tous les documents relatifs aux véhicules, tels que l'assurance, l'enregistrement, les permis de conduire requis, et tout autre document réglementaire."),

            ("Comment ajouter et gérer des photos de véhicules ?", "Les photos de véhicules peuvent être ajoutées et gérées dans le menu 'Album photos', qui permet aux utilisateurs de télécharger et de gérer des photos de chaque véhicule."),

            ("Que permet de faire le menu Marques & Modèles ?", "Le menu Marques & Modèles fournit un outil pour gérer les marques et modèles de véhicules dans l'inventaire. Cela inclut l'ajout de nouvelles marques ou modèles, la modification des informations existantes, ou la suppression de marques ou modèles obsolètes."),

            ("Comment créer et gérer les profils des clients ?", "Les profils des clients peuvent être créés et gérés dans le menu 'Edition clients'. Les utilisateurs peuvent ajouter de nouveaux clients, modifier les informations des clients existants, ou supprimer des clients de la base de données."),

            ("Que montre la liste des clients dans le menu Clients & Tiers ?", "La liste des clients offre une vue d'ensemble de tous les clients enregistrés dans le système. Elle peut être utilisée pour accéder rapidement aux profils des clients, vérifier l'historique de leurs transactions, et gérer leurs réservations actuelles ou futures."),

            ("Comment gérer les documents des clients ?", "Les documents des clients peuvent être gérés dans le menu 'Documents des clients', qui permet aux utilisateurs de télécharger, de stocker, et de gérer les documents relatifs aux clients, tels que les copies de permis de conduire, les pièces d'identité, et tout autre document nécessaire."),

            ("Que contient le menu Nos agents ?", "Le menu Nos agents est destiné à la gestion des agents qui travaillent pour l'entreprise. Les utilisateurs peuvent ajouter des informations sur les agents, suivre leurs performances, et gérer leurs affectations."),

            ("Comment gérer les conducteurs associés à l'entreprise ?", "Les conducteurs associés à l'entreprise peuvent être gérés dans le menu 'Nos conducteurs'. Ce menu permet de gérer les conducteurs, y compris leurs qualifications, disponibilités, et évaluations."),

            ("Comment créer une nouvelle réservation pour un client ?", "Pour créer une nouvelle réservation, utilisez le menu 'Nouvelle réservation'. Ce processus inclut la sélection du véhicule, la définition des dates de début et de fin de la location, ainsi que la saisie des informations client et des conditions particulières de la location."),

            ("Que montre la liste des réservations ?", "La liste des réservations fournit une vue complète de toutes les réservations actives. Les utilisateurs peuvent consulter les détails de chaque réservation, modifier des réservations existantes, ou annuler des réservations si nécessaire."),

            ("Comment accéder à l'historique des réservations ?", "L'historique des réservations est accessible via le menu 'Historique des Réservations'. Il donne accès à l'historique complet des réservations effectuées, permettant de rechercher des informations sur les locations précédentes."),

            ("Que montre le planning des réservations ?", "Le planning des réservations affiche un calendrier des réservations, offrant une vue d'ensemble de l'utilisation des véhicules à une date donnée. Cela aide à planifier les réservations futures et à maximiser l'utilisation du parc de véhicules."),

            ("Quels véhicules sont disponibles pour une location immédiate ?", "Les véhicules disponibles pour une location immédiate sont indiqués dans le menu 'Disponibilité immédiate', permettant une gestion rapide et efficace des demandes de dernière minute."),

            ("Comment créer et gérer des contrats de location ?", "Les contrats de location peuvent être créés et gérés dans le menu 'Edition contrat'. Les utilisateurs peuvent personnaliser les contrats en fonction des besoins spécifiques de chaque location."),

            ("Que contient le menu Liste de contrats ?", "Le menu Liste de contrats offre une vue d'ensemble de tous les contrats de location créés, permettant aux utilisateurs de consulter, de modifier, ou d'archiver les contrats existants."),

            ("Comment gérer les réservations effectuées à travers le site web ou l'application mobile ?", "Les réservations effectuées via le site web ou l'application mobile peuvent être gérées dans le menu 'Liste des réservations' sous 'Site Web & Applis'. Cela permet de visualiser et de gérer toutes ces réservations, y compris la confirmation de nouvelles réservations et la modification de réservations existantes."),

            ("Comment gérer les demandes de réservation en attente de confirmation ?", "Les demandes de réservation en attente de confirmation sont gérées dans le menu 'Pré-réservations'. Cela permet de vérifier les informations fournies par les clients et de confirmer ou de refuser les réservations en fonction de la disponibilité et des critères de l'entreprise."),

            ("Comment gérer les réservations faites par des partenaires commerciaux ?", "Les réservations faites par des partenaires commerciaux sont gérées dans le menu 'Pré-résa de partenaires'. Ce sous-menu aide à gérer ces réservations séparément, permettant une coordination et une collaboration efficaces avec les partenaires."),

            ("Comment contrôler quels modèles de véhicules sont affichés sur le site web et l'application ?", "Le contrôle des modèles de véhicules affichés sur le site web et l'application se fait via le menu 'Modèles visibles sur Site'. Cela permet de contrôler quels modèles sont affichés, y compris les informations détaillées sur chaque modèle."),

            ("Comment gérer les variations de tarifs selon les saisons ou les périodes spécifiques ?", "Les variations de tarifs selon les saisons ou les périodes spécifiques sont gérées dans le menu 'Périodes tarifaires'. Cela permet une flexibilité tarifaire pour maximiser les revenus et répondre à la demande."),

            ("Comment créer et modifier différentes catégories de prix pour les véhicules ?", "Les différentes catégories de prix pour les véhicules peuvent être créées et modifiées dans le menu 'Catégories tarifaires'. Cela permet d'offrir aux clients une variété d'options en fonction de leur budget."),

            ("Comment définir les prix de location pour chaque véhicule ?", "Les prix de location pour chaque véhicule sont définis dans le menu 'Tarifs'. Cela inclut la définition des prix pour chaque véhicule ou catégorie de véhicule, y compris les promotions ou les réductions spéciales."),

            ("Comment gérer les frais d'abandon pour les véhicules ?", "Les frais d'abandon sont gérés dans le menu 'Frais d'abandon'. Ce menu permet de gérer les frais appliqués lorsque les clients décident de laisser le véhicule dans un lieu différent de celui de la prise en charge."),

            ("Comment définir les coûts associés au déplacement des véhicules entre succursales ?", "Les coûts associés au déplacement des véhicules entre succursales sont définis dans le menu 'Frais de Convoyage'. Ce menu permet de définir les coûts pour le déplacement des véhicules d'une succursale à une autre."),

            ("Comment permettre aux clients de réserver des équipements supplémentaires ?", "Les équipements supplémentaires peuvent être réservés par les clients via le menu 'Extra/Accessoires'. Ce menu permet aux clients de réserver des équipements supplémentaires avec leur location de véhicule, tels que des sièges bébé, des GPS, ou des barres de toit."),

            ("Comment gérer les tarifs pour le kilométrage supplémentaire ?", "Les tarifs pour le kilométrage supplémentaire sont gérés dans le menu 'Extra Kilométrage'. Ce menu permet de gérer les tarifs pour le kilométrage au-delà de ce qui est inclus dans la location standard."),

            ("Comment définir les options de couverture d'assurance pour les clients ?", "Les options de couverture d'assurance pour les clients sont définies dans le menu 'Franchise & rachats'. Ce menu permet de définir les options de couverture d'assurance et de franchise pour les clients, permettant une personnalisation selon les besoins."),

            ("Comment offrir des packages d'assurance à tarif réduit ?", "Les packages d'assurance à tarif réduit sont offerts via le menu 'Packs de franchise'. Ce menu permet d'offrir des packages d'assurance à tarif réduit avec des conditions spécifiques pour attirer et fidéliser les clients."),

            ("Comment gérer les codes promotionnels pour les réservations ?", "Les codes promotionnels pour les réservations sont gérés dans le menu 'Codes Promo'. Ce menu permet de gérer les codes promotionnels offrant des réductions sur les réservations, utilisés pour des campagnes marketing spécifiques."),

            ("Comment mettre en place des restrictions ou des modifications tarifaires ?", "Les restrictions ou modifications tarifaires sont mises en place via le menu 'Redu. Maj. et blocages'. Ce menu permet de mettre en place des restrictions ou des modifications tarifaires pour certaines périodes, modèles, ou conditions."),

            ("Comment collecter et gérer les avis des clients sur leurs expériences de location ?", "Les avis des clients sont collectés et gérés dans le menu 'Avis Clients'. Ce menu permet de collecter et de gérer les avis des clients sur leurs expériences de location, fournissant des retours précieux pour l'amélioration des services."),

            ("Comment gérer les informations concernant les lieux de prise en charge et de retour des véhicules ?", "Les informations concernant les lieux de prise en charge et de retour des véhicules sont gérées dans le menu 'Agences et points de livraison'. Ce menu permet de gérer les informations concernant les différents lieux de prise en charge et de retour disponibles pour les clients."),

            ("Comment définir les termes et conditions de location ?", "Les termes et conditions de location sont définis dans le menu 'Conditions générales'. Ce menu permet de définir les termes et conditions de location que les clients doivent accepter lors de la réservation en ligne."),

            ("Que contient le menu FAQs ?", "Le menu FAQs fournit des réponses aux questions fréquemment posées par les clients, aidant à réduire le nombre de demandes de renseignements client."),

            ("Comment publier des nouvelles et des promotions sur le site web et l'application ?", "Les nouvelles et promotions sont publiées via le menu 'News & Promos'. Ce menu permet de publier des nouvelles et des promotions sur le site web et l'application pour informer les clients des offres spéciales et des mises à jour de l'entreprise."),

            ("Comment afficher la politique de confidentialité de l'entreprise ?", "La politique de confidentialité de l'entreprise est affichée via le menu 'Politique de confidentialité'. Ce menu permet d'afficher la politique de confidentialité concernant la collecte, l'utilisation et la protection des données personnelles des utilisateurs."),

            ("Comment assurer la synchronisation des données sur le site web et l'application mobile ?", "La synchronisation des données est assurée via le menu 'Synchronisation des données'. Ce sous-menu est crucial pour assurer que toutes les données affichées sur le site web et dans l'application mobile sont à jour."),

        ]

        # Définir les options pour les nouveaux champs
        priorities = ['low', 'medium', 'high']
        response_sources = ['ia', 'agent']


        # Ajouter des questions pour différents bots
        for bot_id in [1, 2]:
            for question, answer in qa:
                priority = choice(priorities)
                response_source = choice(response_sources)

                # Only assign agent_user_id if response_source is 'agent' AND we have agents available
                if response_source == 'agent' and len([id for id in agent_user_ids if id is not None]) > 0:
                    # Filter out None values for agent responses
                    available_agents = [id for id in agent_user_ids if id is not None]
                    agent_user_id = choice(available_agents)
                else:
                    agent_user_id = None
                    response_source = 'ia'  # Force to IA if no agents available

                new_faq = QuestionResponseLog(
                    id_bot=bot_id,
                    user_id=randint(1, 3),
                    question=question,
                    response=answer,
                    feedback=randint(1, 5),
                    feedback_text=choice(['Très utile', 'Bonne réponse', 'Parfait', 'Merci', None]),
                    priority=PriorityEnum(priority),
                    response_source=ResponseSourceEnum(response_source),
                    agent_user_id=agent_user_id,
                    timeQuestion=datetime.now(),
                )
                session.add(new_faq)


        session.commit()
        print(f'{INFO_LOG} Questions ajoutées avec les nouveaux champs.')

    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de l\'ajout des questions: {e}')
        session.rollback()
        raise
    finally:
        session.close()

def add_page_number_to_embeddings():
    """Ajouter le champ pageNumber à la table embeddings_data"""
    session = get_session()
    try:
        inspector = inspect(engine)
        columns = [col['name'] for col in inspector.get_columns('embeddings_data')]

        # Ajouter la colonne pageNumber si elle n'existe pas
        if 'pageNumber' not in columns:
            session.execute(text(
                'ALTER TABLE public.embeddings_data ADD COLUMN "pageNumber" integer'
            ))
            print(f'{INFO_LOG} Colonne pageNumber ajoutée.')

        session.commit()

    except Exception as e:
        print(f'{ERROR_LOG} Erreur lors de l\'ajout de la colonne pageNumber: {e}')
        session.rollback()
        raise
    finally:
        session.close()


@click.command()
def add_page_number_field():
    """Commande pour ajouter le champ pageNumber à la table embeddings_data"""
    add_page_number_to_embeddings()

# Commandes Click
mycommands.add_command(rebuild_all_tables)
mycommands.add_command(generate_db_data)
mycommands.add_command(embbed_doc)
mycommands.add_command(add_page_number_field)

if __name__ == '__main__':
    mycommands()
