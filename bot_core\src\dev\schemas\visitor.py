from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr


class VisitorBase(BaseModel):
    userName: Optional[str] = None
    email: Optional[EmailStr] = None
    createDate: Optional[datetime] = None
    lastLogin: Optional[datetime] = None
    additionalInfo: Optional[str] = None
    sessionId: Optional[int] = None  # Changement ici

    class Config:
        from_attributes = True

class VisitorCreate(VisitorBase):
    pass

class VisitorUpdate(VisitorBase):
    pass

class VisitorInDB(VisitorBase):
    userId: int  # Identifiant unique pour le visiteur

class VisitorOut(VisitorInDB):
    pass
