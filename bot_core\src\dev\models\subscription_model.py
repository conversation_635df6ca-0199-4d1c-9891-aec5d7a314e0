from sqlalchemy import Column, Integer, DateTime, ForeignKey, SmallInteger
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base


class Subscription(Base):
    __tablename__ = "subscription"
    idSubscription = Column(Integer, primary_key=True, index=True, autoincrement=True)
    idPack = Column(Integer, ForeignKey('pack.idPack'))
    userId = Column(Integer, ForeignKey('user.userId'))
    startDate = Column(DateTime)
    endDate = Column(DateTime)
    status = Column(SmallInteger)
    pack = relationship("Pack", back_populates="subscriptions")
    user = relationship("User", back_populates="subscriptions")
    payments = relationship("Payment", back_populates="subscriptions", cascade="all, delete-orphan")
