from elasticsearch import Elasticsearch
from src.dev.services.rag.rag_processor_factory import get_rag_processor
from src.dev.config.config import get_settings
import logging

def safe_es_vector_search(query: str, bot_id: int):
    """
    Centralized function for Elasticsearch search with fallback handling.
    Returns: tuple of (es_results, vector_value) or (None, None) if failure.
    """
    logger = logging.getLogger("uvicorn")
    settings = get_settings()

    try:
        es_client = Elasticsearch(settings.ELASTIC_SERVER)
        processor = get_rag_processor()
        vector_value = processor.query_embedding(query)

        query_body = {
            "bool": {
                "must": {
                    "knn": {
                        "field": "question_embedding",
                        "query_vector": vector_value,
                        "k": 1,
                        "num_candidates": 100
                    }
                },
                "filter": {
                    "term": {"idBot": bot_id}
                }
            }
        }

        results = es_client.search(
            index="questions",
            query=query_body,
            source_includes=["question", "answer", "idBot", "response"]
        )

        logger.info(f"✅ Elasticsearch query success for bot {bot_id}")
        return results, vector_value

    except Exception as e:
        logger.warning(f"⚠️ Elasticsearch search failed: {str(e)}")
        return None, None
