import os
import logging
from datetime import datetime, timedelta
from src.dev.utils.database import SessionLocal
from src.dev.models.conversation import ConversationTempDocument

logger = logging.getLogger("uvicorn")

def cleanup_expired_temp_documents():
    """
    Nettoie les documents temporaires expirés de la base de données et du système de fichiers.
    """
    db = SessionLocal()
    try:
        # Trouver les documents temporaires expirés
        expired_docs = db.query(ConversationTempDocument).filter(
            ConversationTempDocument.expiresAt < datetime.utcnow()
        ).all()
        
        for doc in expired_docs:
            # Supprimer le fichier physique s'il existe
            if os.path.exists(doc.filepath):
                try:
                    os.remove(doc.filepath)
                    logger.info(f"Fichier supprimé: {doc.filepath}")
                except Exception as e:
                    logger.error(f"Erreur lors de la suppression du fichier {doc.filepath}: {e}")
            
            # Supprimer l'entrée de la base de données
            db.delete(doc)
        
        db.commit()
        logger.info(f"{len(expired_docs)} documents temporaires expirés nettoyés")
        
    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des documents temporaires: {e}")
        db.rollback()
    finally:
        db.close()

def cleanup_old_conversations():
    """
    Nettoie les conversations inactives selon les règles TTL.
    """
    db = SessionLocal()
    try:
        # Supprimer les conversations fermées depuis plus de 30 jours
        # Vous pouvez ajuster ce délai selon vos besoins
        pass
        
    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des anciennes conversations: {e}")
        db.rollback()
    finally:
        db.close()