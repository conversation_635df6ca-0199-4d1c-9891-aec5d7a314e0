import os
import logging
from datetime import datetime, timedelta
from src.dev.utils.database import SessionLocal
from src.dev.models.conversation import ConversationTempDocument, UserTempData, UserTempChunk

logger = logging.getLogger("uvicorn")

def cleanup_expired_temp_documents():
    """
    Nettoie les documents temporaires expirés de la base de données et du système de fichiers.
    """
    db = SessionLocal()
    try:
        # Trouver les documents temporaires expirés
        expired_docs = db.query(ConversationTempDocument).filter(
            ConversationTempDocument.expiresAt < datetime.utcnow()
        ).all()
        
        for doc in expired_docs:
            # Supprimer le fichier physique s'il existe
            if os.path.exists(doc.filepath):
                try:
                    os.remove(doc.filepath)
                    logger.info(f"Fichier supprimé: {doc.filepath}")
                except Exception as e:
                    logger.error(f"Erreur lors de la suppression du fichier {doc.filepath}: {e}")
            
            # Supprimer l'entrée de la base de données
            db.delete(doc)
        
        db.commit()
        logger.info(f"{len(expired_docs)} documents temporaires expirés nettoyés")
        
    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des documents temporaires: {e}")
        db.rollback()
    finally:
        db.close()

def cleanup_old_conversations():
    """
    Nettoie les conversations inactives selon les règles TTL.
    """
    db = SessionLocal()
    try:
        # Supprimer les conversations fermées depuis plus de 30 jours
        # Vous pouvez ajuster ce délai selon vos besoins
        pass

    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des anciennes conversations: {e}")
        db.rollback()
    finally:
        db.close()


def cleanup_expired_user_temp_data():
    """
    Nettoie les données utilisateur temporaires expirées (pipeline RAG temporaire).
    Supprime les données utilisateur et leurs chunks associés après expiration.
    """
    db = SessionLocal()
    try:
        # Trouver les données utilisateur temporaires expirées
        expired_user_data = db.query(UserTempData).filter(
            UserTempData.expires_at < datetime.utcnow()
        ).all()

        for user_data in expired_user_data:
            # Les chunks associés seront supprimés automatiquement grâce à cascade="all, delete-orphan"
            db.delete(user_data)
            logger.info(f"Données utilisateur temporaires supprimées pour user_id: {user_data.user_id}")

        db.commit()
        logger.info(f"{len(expired_user_data)} ensembles de données utilisateur temporaires expirés nettoyés")

    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des données utilisateur temporaires: {e}")
        db.rollback()
    finally:
        db.close()


def cleanup_inactive_user_temp_data(inactive_days=2):
    """
    Nettoie les données utilisateur temporaires inactives depuis X jours.
    """
    db = SessionLocal()
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=inactive_days)

        # Trouver les données utilisateur inactives
        inactive_user_data = db.query(UserTempData).filter(
            UserTempData.last_activity < cutoff_date
        ).all()

        for user_data in inactive_user_data:
            db.delete(user_data)
            logger.info(f"Données utilisateur inactives supprimées pour user_id: {user_data.user_id}")

        db.commit()
        logger.info(f"{len(inactive_user_data)} ensembles de données utilisateur inactives nettoyés")

    except Exception as e:
        logger.error(f"Erreur lors du nettoyage des données utilisateur inactives: {e}")
        db.rollback()
    finally:
        db.close()