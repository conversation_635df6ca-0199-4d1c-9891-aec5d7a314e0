from typing import Annotated
from fastapi import (
    APIRouter,
    Depends,
    Query,
    Response,
    status,
)
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from src.dev.schemas.admin_schema import AdminSchema, CreateAdminSchema
from src.dev.models.enums.user_type import UserType
from src.dev.utils.auth import <PERSON><PERSON><PERSON><PERSON>
from src.dev.const import AUTH_URL, AUTH_TAGS
from src.dev.schemas.auth import CreateUserSchema, TokenSchema, UserSchema
from src.dev.services.auth.auth import ApiKeyService, AuthService, api_key_security, get_current_user
from src.dev.utils.dependencies import get_db



router = APIRouter(prefix="/" + AUTH_URL, tags=AUTH_TAGS)


@router.post("/token", response_model=TokenSchema)
async def authenticate(
    login: OAuth2PasswordRequestForm = Depends(),
    session: Session = Depends(get_db),
) -> TokenSchema | None:
    return AuthService(session).authenticate(login)

@router.post("/register", response_model=AdminSchema)
async def register(admin: CreateAdminSchema, response: Response, session: Session = Depends(get_db)):
  response.status_code = status.HTTP_201_CREATED
  return AuthService(session).create_account(admin)
   
@router.get("/refresh")
async def get_new_access_token(refresh_token: Annotated[str, Query(alias="refresh-token")] ,session: Session = Depends(get_db)):
   return AuthService(session).get_new_access_token(refresh_token)

# route pour tester api key
@router.get("/api-key-needy")
async def secret(api_key:str = Depends(api_key_security)):
   return {'message': 'cette route protégée est accessible à vous via votre clé API.'}

@router.get("/bots/{id_bot}/api-key/new")
async def secret(
            id_bot: int
            ,user: UserSchema = Depends(
                                    RoleChecker(allowed_roles=
                                                [UserType.admin, 
                                                UserType.client]))
            ,session: Session = Depends(get_db)
            ):
   
   api_key = ApiKeyService(session).create_api_key(user, id_bot)
   return {'apikey':api_key}

@router.get("/bots/{id_bot}/api-key/revoke")
async def secret(id_bot: int, user: UserSchema = Depends(
                                    RoleChecker(allowed_roles=
                                                [UserType.admin, 
                                                UserType.client]))
                 ,session: Session = Depends(get_db)):
   ApiKeyService(session).revoke_key(user, id_bot)
   return {'message':'votre clé API a été supprimée avec succès'}

@router.get("/users/me")
async def get_current_user_info(user: UserSchema = Depends(get_current_user)):
    return user