import os
import logging
from typing import Dict, List, Tuple, Any
from abc import ABC, abstractmethod
from dotenv import load_dotenv
from nltk.tokenize import word_tokenize

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Interface abstraite pour les processeurs RAG
class AbstractRAGProcessor(ABC):
    @abstractmethod
    def preprocess_text(self, text: str) -> str:
        """Prétraitement du texte d'entrée"""
        pass
    
    @abstractmethod
    def semantic_splitter(self, text: Any) -> List[str]:
        """Découpe sémantique du texte en chunks"""
        pass
    
    @abstractmethod
    def generate_embedding(self, text: str) -> List[float]:
        """Génère l'embedding pour un texte donné"""
        pass
    
    @abstractmethod
    def process_document(self, document_text: Any) -> Tuple[List[str], List[List[float]]]:
        """Traite un document pour extraire les chunks et générer les embeddings"""
        pass
    
    @abstractmethod
    def node_embedding(self, documents: Any) -> List[List[float]]:
        """Génère des embeddings pour une liste de documents/nodes"""
        pass
    
    @abstractmethod
    def vectorize_document_and_store_in_database(
        self, document_url: str, bot_id: int, doc_id: int, db
    ) -> None:
        """Vectorise un document et le stocke dans la base de données"""
        pass
    
    @abstractmethod
    def process_rag_query(
        self, prompt: str, document_key: str, db
    ) -> str:
        """Traite une requête RAG"""
        pass
    
    @abstractmethod
    def query_embedding(self, query: str) -> List[float]:
        """Génère l'embedding pour une requête"""
        pass

    # === Tokenisation pour BM25 ===
    def tokenize_chunk(chunk_text: str) -> list:
        return word_tokenize(preprocess_text(chunk_text).lower())

    # Add this to your AbstractRAGProcessor class
    @abstractmethod
    def hybrid_retrieval(self, query: str, chunks: List[str], tokenized_corpus: List[List[str]], top_k: int = 5) -> List[str]:
        """Perform hybrid retrieval (vector + lexical)"""
        pass

    @abstractmethod
    def load_chunks_with_tokens(self, document_key: str, db) -> List[Dict]:
        """Charge les chunks avec leurs tokens depuis la base de données"""
        pass

    @abstractmethod
    def rerank_passages(self, query: str, passages: List[Dict], top_k: int = 10) -> List[Dict]:
        """Rerank retrieved passages based on the query"""
        pass



