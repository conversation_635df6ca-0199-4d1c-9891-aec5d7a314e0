#!/usr/bin/env python3

from datetime import datetime, timezone
import sys
from pathlib import Path


from sqlalchemy.orm import Session, joinedload
from sqlalchemy import exists

from src.dev.services.rag.rag_processor_factory import get_rag_processor
from src.dev.models.bot_model import Bo<PERSON>
from src.dev.utils.database import SessionLocal
from src.dev.models.documentation_model import Documentation
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.api import ClientController

from src.dev.services.rag.vectorizer import vectorize_and_store_document


def process_document(document, db: Session):
    """
    Traite un document : extrait, découpe, vectorise et sauvegarde.
    """
    print(f"[TRAITEMENT] Document ID {document.idDocumentation}")

    res = vectorize_and_store_document(bot_id=1,document_id=document.idDocumentation, db=db, force=True)
    
    print(res)



def process_all_unprocessed(db: Session):
    """
    Trouve et traite tous les documents non vectorisés ET liés à un bot.
    """
    print("[INFO] Recherche des documents non vectorisés et liés à un bot...")

    # Sous-requête : vérifie si le document existe dans bot_includedocumentation
    subquery = db.query(
        exists().where(
            BotIncludeDocumentation.idDocumentation == Documentation.idDocumentation
        )
    )

    # Requête principale : documents non vectorisés ET liés à un bot
    documents = (
        db.query(Documentation)
        .join(BotIncludeDocumentation)  # ← Jointure directe
        .options(joinedload(Documentation.bot_includes))
        .filter(Documentation.embedded == False)
        .all()
    )

    if not documents:
        print("[INFO] Aucun document à traiter.")
        return []

    results = []
    for doc in documents:
        if not doc.embedded:
            result = process_document(doc, db)
            if result:
                results.append(result)

    return results


if __name__ == "__main__":
    db = SessionLocal()
    try:
        process_all_unprocessed(db)
    finally:
        db.close()