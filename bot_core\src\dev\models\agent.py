# from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Enum as SAEnum
# from src.dev.models.user_model import User
# import enum


# class AgentRoleEnum(str, enum.Enum):
#     admin = "admin"
#     supervisor = "supervisor"
#     operator = "operator"
#     bot = "bot"


# class Agent(User):
#     __tablename__ = 'agents' 

#     userId = Column(Integer, ForeignKey('user.userId'), primary_key=True, index=True, autoincrement=True)
#     role = Column(SAEnum(AgentRoleEnum), nullable=False)
#     email = Column(String, nullable=False)
#     is_assigned = Column(Boolean, default=False)

#     __mapper_args__ = {
#         'polymorphic_identity': 'agents'
#     }
