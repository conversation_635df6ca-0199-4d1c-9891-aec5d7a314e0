# src/dev/api/routes/souscription_routes.py
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.auth import UserSchema
from src.dev.utils.auth import <PERSON><PERSON><PERSON><PERSON>
from src.dev.schemas.subscription_schemas import SouscriptionCreate, SouscriptionUpdate, SubscriptionSchema
from src.dev.services.souscription_service import create_souscription, update_souscription, delete_souscription, get_souscriptions
from src.dev.utils.dependencies import get_db

souscription_routes = APIRouter(prefix="/api/v1")

@souscription_routes.get("/souscriptions", response_model=list[SubscriptionSchema])
def get_souscriptions_route(db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscriptions = get_souscriptions(db)
    return [
        SubscriptionSchema(
            id=db_souscription.idSubscription,
            idSubscription=db_souscription.idSubscription,
            idPack=db_souscription.pack.idPack,
            userId=db_souscription.user.userId,
            nom_du_pack=db_souscription.pack.packName,
            nom_de_utilisateur=db_souscription.user.userName,
            startDate=db_souscription.startDate.isoformat(),
            endDate=db_souscription.endDate.isoformat(),
            status=db_souscription.status
        )
        for db_souscription in db_souscriptions
    ]

@souscription_routes.post("/createSouscription", response_model=SubscriptionSchema)
def create_souscription_route(souscription: SouscriptionCreate, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscription = create_souscription(souscription, db)
    return SubscriptionSchema(
        id=db_souscription.idSubscription,
        idSubscription=db_souscription.idSubscription,
        idPack=db_souscription.pack.idPack,
        userId=db_souscription.user.userId,
        nom_du_pack=db_souscription.pack.packName,
        nom_de_utilisateur=db_souscription.user.userName,
        startDate=db_souscription.startDate.isoformat(),
        endDate=db_souscription.endDate.isoformat(),
        status=db_souscription.status
    )

@souscription_routes.put("/updateSouscription/{idSubscription}", response_model=SubscriptionSchema)
def update_souscription_route(idSubscription: int, souscription: SouscriptionUpdate, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    db_souscription = update_souscription(idSubscription, souscription, db)
    return SubscriptionSchema(
        id=db_souscription.idSubscription,
        idSubscription=db_souscription.idSubscription,
        idPack=db_souscription.pack.idPack,
        userId=db_souscription.user.userId,
        nom_du_pack=db_souscription.pack.packName,
        nom_de_utilisateur=db_souscription.user.userName,
        startDate=db_souscription.startDate.isoformat(),
        endDate=db_souscription.endDate.isoformat(),
        status=db_souscription.status
    )

@souscription_routes.delete("/deleteSouscription/{idSubscription}", response_model=dict)
def delete_souscription_route(idSubscription: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    return delete_souscription(idSubscription, db)
