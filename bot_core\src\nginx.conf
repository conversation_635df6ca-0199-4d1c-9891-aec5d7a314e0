
#user  nobody;
worker_processes  1;



events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;



    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;


    # Configuration du serveur
    server {
        listen       80;
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        # Configuration pour votre API FastAPI
        location /api/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Configuration pour servir les fichiers statiques
        location /files/ {
            alias "C:\Users\<USER>\docubot\docubot\bot_core\src\dev\assets";
            
            # Activer l'indexation pour debug (à désactiver en production)
            autoindex on;
            
            # Logs détaillés pour debugging
            access_log logs/files_access.log;
            error_log logs/files_error.log;
            
            # Headers pour le debugging
            add_header X-Debug-Path "$request_filename" always;
            
            # Configuration pour les PDF
            location ~* \.pdf$ {
                add_header Content-Type application/pdf;
                add_header Content-Disposition "inline";
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
            
            # Sécurité : empêcher l'accès aux fichiers cachés
            location ~ /\. {
                deny all;
            }
        }

        # Configuration par défaut
        location / {
            root   html;
            index  index.html index.htm;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}