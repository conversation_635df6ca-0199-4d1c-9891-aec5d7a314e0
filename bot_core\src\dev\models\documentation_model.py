from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import Enum as SQLAEnum
from src.dev.models.enums.DocumentTypeEnum import DocumentTypeEnum
from src.dev.utils.database import Base
from datetime import datetime
from typing import List, Optional


class Documentation(Base):
    __tablename__ = 'documentation'

    idDocumentation = Column(Integer, primary_key=True, index=True, autoincrement=True)
    title = Column(String(60), nullable=False)
    url: Mapped[Optional[str]] = mapped_column(String, nullable=False)
    type: Mapped[Optional[DocumentTypeEnum]] = mapped_column(
        SQLAEnum(DocumentTypeEnum, 
                 name="documenttypeenum", 
                 native_enum=False, 
                 values_callable=lambda enum_cls: [e.value for e in enum_cls]), 
                 nullable=False
    )
    embedded: Mapped[bool] = mapped_column(Boolean, nullable=False)
    #lastUpdated = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    bot_includes = relationship("BotIncludeDocumentation", back_populates="documentation")
