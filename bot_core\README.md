# Bot Core Service - Backend

Ce projet constitue le backend pour le Bot Core Service, utilisant Python et FastAPI. Il intègre des modèles d'intelligence artificielle comme LLaMA, GPT, ou Mistral pour fournir une interface de traitement de langage naturel basée sur la technologie de RAG (Retrieval-Augmented Generation) à partir de documents PDF.

## Structure du Projet

Le projet est organisé en plusieurs dossiers principaux :


  /bot_core
  |-- 
  |-- __init__.py              # Initialise le package app
  |-- main.py                  # Point d'entrée principal de l'application FastAPI
  |-- /api                         # Contient les contrôleurs et routes
  |-- /controllers             # Logique de contrôle pour différentes routes
  |   |-- bot_controller.py    # Traite les requêtes API pour les interactions avec le bot
  |   |-- user_controller.py   # Gère les requêtes liées aux utilisateurs
  |-- /routes                  # Définitions des routes API
  |   |-- bot_routes.py        # Routes pour les interactions bot
  |   |-- user_routes.py       # Routes pour la gestion des utilisateurs
  |-- /middlewares             # Middlewares pour la gestion des requêtes
  |   |-- auth_middleware.py   # Middleware pour l'authentification et l'autorisation
  |-- /services                    # Services métiers
  |-- /ai                      # Services pour intégrer les modèles d'IA
  |   |-- ai_service.py        # Service pour interagir avec les modèles d'IA
  |-- /rag                     # Services pour la génération augmentée par récupération
  |   |-- rag_processor.py     # Logique pour le traitement RAG
  |-- /models                      # Définitions des modèles de données
  |   |-- user_model.py        # Modèles pour les utilisateurs
  |   |-- bot_model.py         # Modèles pour les données de bot
  |-- /utils                       # Utilitaires divers
  |   |-- logger.py            # Utilitaires pour le logging
  |-- /config                      # Configuration de l'application
  |   |-- config.py            # Fichiers de configuration globale
  |-- requirements.txt             # Dépendances du projet
  |-- README.md                    # Documentation du projet


Explications  
Modules Python (__init__.py):
Chaque répertoire contenant du code Python est considéré comme un package, et pour cela, un fichier __init__.py (potentiellement vide) est nécessaire pour que Python reconnaisse le répertoire comme tel.
Fichiers Python:
Tous les scripts et modules JavaScript sont convertis en fichiers Python avec des conventions de nommage Pythoniques (par exemple, snake_case pour les noms de fichiers et de fonctions).
Intégration FastAPI:
Le fichier main.py contient l'instance de l'application FastAPI et les inclut de toutes les routes. Il sert également à configurer les middlewares et toute autre configuration globale nécessaire au démarrage de l'application.
Configuration et Environnement:
Le fichier config.py dans le répertoire /config peut être utilisé pour gérer différentes configurations et variables d'environnement, remplaçant l'usage typique de fichiers de configuration externes ou de variables d'environnement directement intégrées.


## Prérequis a installer depuis le fichier requirements.txt

- fastapi
- redis
- types-redis
- uvicorn (pour le serveur ASGI)
- sqlalchemy
- torch
- PyPDF2
- psycopg2-binary
- requests
- beautifulsoup4
- pdfplumber
- langchain
- langchain-community
- sentence-transformers
- langdetect
- rank-bm25
- einops


### Configuration 
pip install -r requirements.txt 

[//]: # (- Python 3.8+)
[//]: # (- FastAPI)
[//]: # (- Uvicorn &#40;pour le serveur ASGI&#41;)
[//]: # (- PostgreSQL)
[//]: # (- Elasticsearch)
[//]: # (ollama)

### Installation
### 1. Clone the Repository

Fetch la derniere version de la branche `develop` :

```bash
git fetch origin
git checkout develop
git merge origin/develop
```

### 2. Configuration de la Base de Données :
 - Configuration de la Base de Données :
 - Installez PostgreSQL et pgAdmin.
 - Créez un utilisateur nommé postgres avec le mot de passe docuBot111.
 - Importez la base de données située dans docubot/Database/database_postgres.sql.
 - Executez ```CREATE EXTENSION IF NOT EXISTS vector;``` dans pgvector
 - Démarrez le serveur PostgreSQL.

Si vous utilisez une configuration différente, modifiez le fichier docubot/bot_core/config/config.py avec vos informations, 
puis ajoutez ce fichier au .gitignore pour éviter les modifications sur les configurations d'autres utilisateurs.
Exemple de configuration :

```python
DATABASE_URL = "postgresql://postgres:docuBot111@localhost/chatbotdb"
```

### Database Management Script
Ce script permet de gérer la base de données de l'application, incluant la création des tables, la génération de données de test, et l'incorporation de documents.
Le script `database.py` se trouve dans le dossier `bot_core/src/dev/scripts/`.

Le script offre trois commandes principales :

1. `rebuild-all-tables` : Reconstruit toutes les tables de la base de données.
2. `generate-db-data` : Génère des données de test pour la base de données.
3. `embbed-doc` : Incorpore un document dans la base de données.

Pour exécuter le script utilisez la commande suivante :
```python
python3 -m src.dev.scripts.database [COMMAND]
```
Remplacez `[COMMAND]` par l'une des commandes listées ci-dessus.

### Exemples

1. Pour reconstruire toutes les tables :
`python3 -m src.dev.scripts.database rebuild-all-tables`
2. Pour générer des données de test (Assurez-vous d'exécuter tous vos déclencheurs avant d'ajouter de nouvelles données. -docubot/Database/triggers.sql`-) :
`python3 -m src.dev.scripts.database generate-db-data`
3. Pour incorporer un document :
`python3 -m src.dev.scripts.database embbed-doc`
4. Ajouter le champ `page_number` dans la base de données :
`python  -m src.dev.scripts.database add-page-number-field`

### Ajout de nouveaux modèles

Si vous ajoutez un nouveau modèle dans le dossier `models/`, assurez-vous de l'importer dans le script `database.py`. Cela permettra de prendre en compte les changements lors de la reconstruction des tables.

Pour ajouter un nouveau modèle :

1. Créez votre modèle dans le dossier `models/`.
2. Ouvrez `database.py`.
3. Ajoutez une ligne d'import pour votre nouveau modèle, par exemple :
```python
from src.dev.models.your_new_model import YourNewModel
```
Après avoir ajouté le nouveau modèle, vous pouvez exécuter la commande rebuild-all-tables pour mettre à jour la structure de la base de données.
Note importante
Après avoir exécuté la commande rebuild-all-tables, assurez-vous d'exécuter tous les déclencheurs nécessaires avant d'ajouter de nouvelles données. Les déclencheurs se trouvent dans le fichier `docubot/Database/triggers.sql`. N'exécutez pas tout le fichier en une seule fois ; exécutez-le fonction par fonction.



### Installation des Dépendances
### 1.Création de l'Environnement Virtuel :
Créez un environnement virtuel et activez-le :

```bash
python3 -m venv venv
source venv/bin/activate
```
### 2.Installation des Paquets Requis :
Installez les paquets requis à partir du fichier requirements.txt :

```bash
pip3 install -r requirements.txt
```

### 3.Installation de FastAPI et Uvicorn :
Installez les frameworks nécessaires :

```bash
pip3 install fastapi uvicorn
```

### Démarrage de l'Application
### 1.Lancer l'Application :
Exécutez la commande suivante pour démarrer l'application :

```bash
uvicorn main:app --reload
```
### 2.Configurer Visual Studio Code :
Configurez VSCode pour exécuter le projet via le fichier .vscode/launch.json 
(j'ai ajouté le fichier sur git donc essayez d'éxécuter directement avec le bouton debug de visual code)
```json
{
"version": "0.2.0",
"configurations": [
{
"name": "Python Debugger: FastAPI",
"type": "debugpy",
"request": "launch",
"module": "uvicorn",
"args": [
"main:app",
"--reload"
],
"jinja": true
}
]
}
```


## 3. Vérification

Ouvrez [http://127.0.0.1:8000](http://127.0.0.1:8000) dans votre navigateur.
Vous devriez voir :

```json
{"Hello": "World"}
```

Rendez-vous sur [http://127.0.0.1:8000/docs](http://127.0.0.1:8000/docs) pour voir la documentation interactive Swagger des Web Services disponibles.

---

## 4. Authentification dans Swagger UI

Avant d’utiliser les WS protégés, suivez ces étapes pour générer et activer votre token :

### 🔐 Étapes :

1. **Générez un token d’accès** via :

```
POST /api/v1/auth/token
```

- Cliquez sur **Try it out**
- Utilisez :
  - `username`: `<EMAIL>`
  - `password`: `admin12024`
- Cliquez **Execute**
- Copiez la valeur de `access_token`

2. **Cliquez sur le bouton `Authorize` en haut de la page /docs**

- Collez le token dans le champ `X-API-Key`
- Cliquez **Authorize**

3. **Authentifiez-vous aussi via le bloc OAuth2PasswordBearer**

- Entrez à nouveau :
  - `username`: `<EMAIL>`
  - `password`: `admin12024`
- Cliquez **Login**

---

## 5. Tester les Web Services

### 📄 a. Upload d’un document

```
POST /api/client/upload/{client_id}/bot/{bot_id}
```

- Choisissez un fichier PDF de votre machine
- Renseignez :
  - un `client_id` existant dans la table `client`
  - un `bot_id` existant dans la table `bot`

✅ Ce WS :
- Enregistre le fichier dans la table `documentation`
- Ajoute une relation dans `botIncludeDocumentation`

---

### 🧠 b. Vectorisation (chunking + embedding)

```
POST /api/client/process/{client_id}/bot/{bot_id}?document_id={id}
```

- `document_id` : l’ID du document uploadé

✅ Ce WS applique la logique **RAG** : nettoyage, segmentation en chunks, embeddings, et stockage des résultats.
- Enregistre les chunks dans la table `embadding_data`
---

### 🤖 c. Posez une question (RAG + LLM)

```
POST /api/v1/bot/{bot_id}/ask
```

- `bot_id` : ID du bot associé au document
- Dans le corps de requête : posez une question à laquelle le document peut répondre

✅ Ce WS :
- Effectue une recherche sémantique (vectorielle + BM25)
- Retourne les passages les plus pertinents du document
- Appel le LLM et returne une réponse au question

---

## ✅ Exemple rapide de workflow

1. Authentifiez-vous (token + authorize)
2. Uploadez un fichier avec `upload`
3. Lancez `process` avec l’ID du document
4. Posez une question au bot avec `ask`

## 🌐 Installation et configuration de Nginx

### 📥 Téléchargement de Nginx

Vous pouvez télécharger Nginx pour Windows depuis le site officiel :  
🔗 https://nginx.org/en/download.html

Une fois téléchargé, décompressez l'archive ZIP et placez-la dans un dossier accessible, par exemple :  
`C:\Users\<USER>\nginx-1.28.0`

### ⚙️ Configuration du fichier `nginx.conf`

Remplacez le contenu du fichier `nginx.conf` (dans `nginx-1.28.0/conf/`) par la configuration suivante :

```nginx
#user  nobody;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;

        # Proxy vers l’API FastAPI
        location /api/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Fichiers statiques (PDF, etc.)
        location /files/ {
            alias "C:\\Users\\<USER>\\docubot\\docubot\\bot_core\\src\\dev\\assets";
            autoindex on;

            access_log logs/files_access.log;
            error_log logs/files_error.log;

            add_header X-Debug-Path "$request_filename" always;

            location ~* \.pdf$ {
                add_header Content-Type application/pdf;
                add_header Content-Disposition "inline";
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # Sécurité : interdiction des fichiers cachés
            location ~ /\. {
                deny all;
            }
        }

        # Page par défaut
        location / {
            root   html;
            index  index.html index.htm;
        }

        # Pages d’erreur
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}
▶️ Lancer Nginx
cd C:\Users\<USER>\nginx-1.28.0
nginx.exe
## ⚙️ Configuration des variables d'environnement

Assurez-vous de créer un fichier `.env` à la racine du projet (ou dans le dossier approprié selon la structure de votre application).

Ajoutez-y les variables suivantes :

```env
ROOT_STORAGE_PATH=C:\Users\<USER>\Desktop\44\docubot\bot_core\src\dev\assets
BASE_URL=http://localhost

---