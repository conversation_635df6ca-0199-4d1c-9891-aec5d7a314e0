from datetime import datetime

from sqlalchemy import Column, Integer, ForeignKey, String, Enum, DateTime, DECIMAL
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base

payment_status_enum = Enum('pending', 'completed', 'failed', 'refunded', name="payment_status_enum")

class Payment(Base):
    __tablename__ = 'payment'

    paymentId = Column(Integer, primary_key=True, index=True, autoincrement=True)
    userId = Column(Integer, ForeignKey('user.userId'), nullable=False)
    idSubscription = Column(Integer, ForeignKey('subscription.idSubscription'), nullable=True)
    amount = Column(DECIMAL(10, 2), nullable=True)
    paymentMethod = Column(String(255), nullable=True)
    paymentStatus = Column(payment_status_enum, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="payments")
    subscriptions = relationship("Subscription", back_populates='payments')

