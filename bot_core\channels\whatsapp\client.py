import requests

# il faut génerer un token : Meta > WhatsApp > Configuration API
# Pour url : Meta > WhatsApp > Configuration API d'après curl
WHATSAPP_API_URL = "https://graph.facebook.com/v22.0/660441203822356/messages"
WHATSAPP_TOKEN = "EAAJpjPPmNqYBO4UQX5dqXagrHk0xZB5foGeGKKUfpCk1l7jMdBU5AItONGj2Utt5nz8WrOUWQE8fibhZBG20HrZCLRuT53e0sO4gsHdfUKSTuGc67teeVSt6gMEIFPzWhrrXTVSkJufEonteHiXZAdHUMERQOCTlZCffXUqo4EZCiiDE2dh7GiZAfl53ZASdxOZB6W5hQX40mghq2J3FrmMJi6WQmoPKxDEXjfQpQ5ontA3Rl4zAZD"
def send_message(to_number: str, message: str):
    payload = {
        "messaging_product": "whatsapp",
        "to": to_number,
        "type": "text",
        "text": {"body": message}
    }

    headers = {
        "Authorization": f"Bearer {WHATSAPP_TOKEN}",
        "Content-Type": "application/json"
    }

    response = requests.post(WHATSAPP_API_URL, json=payload, headers=headers)
    if response.status_code >= 400:
        print(f"Error sending WhatsApp message: {response.text}")
    else:
        print(f"✅ Message envoyé à {to_number}")
