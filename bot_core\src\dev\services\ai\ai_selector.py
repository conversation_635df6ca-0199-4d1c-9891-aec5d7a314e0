from src.dev.config.models_config import MODELS_CONFIG

# Sélection du modèle le plus adapté
def select_model(total_tokens: int) -> str:
    compatible_models = {
        name: cfg for name, cfg in MODELS_CONFIG.items()
        if total_tokens <= cfg["max_tokens"]
    }
    if not compatible_models:
        raise ValueError(f"Aucun modèle compatible pour {total_tokens} tokens.")
    best_model = min(compatible_models.items(), key=lambda x: x[1]["max_tokens"])
    return best_model[0]