from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path
from typing import List, Optional, Union
from dotenv import load_dotenv
load_dotenv() 
import json
from pydantic import Field
class Settings(BaseSettings):
    APP_NAME: str
    APP_VERSION: str
    DATABASE_URL: str
    OLLAMA_SERVER: str
    FILE_ALLOWED_TYPE: Union[str, List[str]] = Field(default="text/plain,application/pdf")
    FILE_SIZE: int
    FILE_DEFAULT_CHUNK_SIZE: int

    OPENAI_API_KEY: str

    DATABASE_USER: str
    DATABASE_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: int
    DATABASE_NAME: str

    PAYPAL_CLIENT_ID:str
    PAYPAL_CLIENT_SECRET:str
    PAYPAL_API_URL:str

    PAYPAL_RETURN_URL:str
    PAYPAL_CANCEL_URL:str

    ELASTIC_SERVER:str
    KIBANA_SERVER:str



    API_KEY:str
    API_ASK_URL:str
    WHATSAPP_API_URL: str
    WHATSAPP_TOKEN: str

    BOT_CORE:str
    RAG_PROCESSOR_TYPE:str
    ELASTIC_USER:str
    ELASTIC_PASSWORD:str

    ROOT_STORAGE_PATH: str = "C:\\Users\\<USER>\\Desktop\\docubot\\docubot\\bot_core\\src\\dev\\assets"
    BASE_URL: str = "http://localhost"  # ou votre domaine réel

    class Config:
        env_file = str(Path(__file__).resolve().parents[5] / ".env")

def get_settings() -> Settings:
    return Settings()
