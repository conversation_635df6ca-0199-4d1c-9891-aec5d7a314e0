#from src.dev.models.payment_model import Payment
# from src.dev.models.payment_model import Payment
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as SAEnum, Table, Boolean
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base
from src.dev.models.enums.roleEnum import AgentRole
from sqlalchemy import Index

class User(Base):
    __tablename__ = 'user'

    userId = Column(Integer, primary_key=True, index=True, autoincrement=True)
    userName = Column(String(30), nullable=True)
    password = Column(String(255), nullable=True)
    createDate = Column(DateTime, default=datetime.utcnow, nullable=False)
    lastLogin = Column(DateTime, default=datetime.utcnow, nullable=True)
    additionalInfo = Column(Text, nullable=True)
    email = Column(String(50), nullable=True)
    type = Column(String(50))
    phone_number = Column(String(20), nullable=True, unique=True)



    subscriptions = relationship("Subscription", back_populates='user')
#    payments = relationship("Payment", back_populates="user")
    questionResponseLogs = relationship("QuestionResponseLog", back_populates="user")
    visitors = relationship("Visitor", back_populates="user") 
    payments = relationship("Payment", back_populates="user")
    agent = relationship("Agent", back_populates="user", uselist=False)

    # def __init__(self, username, password, create_date, last_login, additional_info, email, type):
    #     self.userName = username
    #     self.password = password
    #     self.createDate = create_date
    #     self.lastLogin = last_login
    #     self.additionalInfo = additional_info
    #     self.email = email
    #     self.type = type
    
    @declared_attr
    def __mapper_args__(cls):
        return {'polymorphic_on': cls.type, 'polymorphic_identity': 'user'}


class Visitor(User):
    __tablename__ = 'visitor'

    userId = Column(Integer, ForeignKey('user.userId'), primary_key=True, index=True, autoincrement=True)
    sessionId = Column(Integer, nullable=True)

    user = relationship("User", back_populates="visitors")

    __mapper_args__ = {
        'polymorphic_identity': 'visitor',
    }


class Client(User):
    __tablename__ = 'client'

    userId = Column(Integer, ForeignKey('user.userId'), primary_key=True, index=True, autoincrement=True)
    adminUserId = Column(Integer, ForeignKey('admin.userId'), nullable=False)
    companyName = Column(String(50), nullable=True)

    admin = relationship("Admin", back_populates="clients", foreign_keys=[adminUserId])

    __mapper_args__ = {
        'polymorphic_identity': 'client',
    }


class Admin(User):
    __tablename__ = 'admin'

    userId = Column(Integer, ForeignKey('user.userId'), primary_key=True, index=True, autoincrement=True)

    clients = relationship("Client", back_populates="admin", foreign_keys=[Client.adminUserId])

    __mapper_args__ = {
        'polymorphic_identity': 'admin',
    }
class Agent(User): 
    __tablename__ = 'agent' 
 
    userId = Column(Integer, ForeignKey('user.userId'), primary_key=True, index=True, autoincrement=True) 
    role = Column(SAEnum(AgentRole, name="agent_role"), nullable=True)
    is_assigned = Column("isAssigned", Boolean, default=False) 
    agent_bot = relationship("AgentBot", back_populates="agent")
    question_responses = relationship("QuestionResponseLog", foreign_keys="QuestionResponseLog.agentUserId", back_populates="agent")

    user = relationship("User", back_populates="agent")
 
    __mapper_args__ = { 
        'polymorphic_identity': 'agent', 
    }
    __table_args__ = (
        Index("ix_agent_isassigned", "isAssigned"),
    )

class RefreshToken(Base):
    __tablename__ = "refreshToken"

    tokenId = Column(Integer, primary_key=True, index = True, autoincrement=True)
    userId = Column(Integer,ForeignKey("user.userId", ondelete="CASCADE"), unique=True)
    dateCreated = Column(DateTime, default=datetime.utcnow, nullable=False)
    refreshToken = Column(String, nullable=False)

    def __init__(self, user_id, refresh_token):
        self.userId = user_id
        self.refreshToken = refresh_token
        self.dateCreated = datetime.utcnow()

    __mapper_args__ = {
        'polymorphic_identity': 'refreshToken',
    }