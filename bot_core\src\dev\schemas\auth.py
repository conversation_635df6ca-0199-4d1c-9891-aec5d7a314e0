
from datetime import datetime
import uuid
from pydantic import EmailStr, Field
from src.dev.models.enums.user_type import UserType
from src.dev.schemas.base import BaseSchema

class CreateUserSchema(BaseSchema):
    username: str
    password: str = Field(min_length=8)
    email: EmailStr
    additional_info: str | None = None


class UserSchema(BaseSchema):
    user_id: int
    username: str
    email: EmailStr
    #TODO: remove password in reponses
    hashed_password: str
    type: UserType
    additional_info: str | None = None


class TokenSchema(BaseSchema):
    access_token: str
    refresh_token: str
    token_type: str


class APIKeySchema(BaseSchema):
    id_bot: int
    api_key: str
    start: datetime
    end: datetime
    is_limit_reached: bool