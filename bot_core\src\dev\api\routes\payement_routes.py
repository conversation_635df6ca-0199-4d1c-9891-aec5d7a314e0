from datetime import datetime, timed<PERSON>ta

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm import Session
from src.dev.models.enums.user_type import UserType
from src.dev.utils.auth import <PERSON><PERSON><PERSON><PERSON>
from src.dev.config.config import Settings, get_settings
from src.dev.models.payment_model import Payment
from src.dev.models.subscription_model import Subscription
from src.dev.schemas.auth import UserSchema
from src.dev.services.auth.auth import get_current_user
from src.dev.services.payment.paypal_service import PayPalService
from src.dev.utils.dependencies import get_db

router = APIRouter(
    prefix="/api/v1",
    tags=["payments"]
)

@router.get("/payment/status")
def check_payment_status(paymentId: int, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    payment = db.query(Payment).filter(Payment.paymentId == paymentId).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    return {"status": payment.paymentStatus}

paypal_service = PayPalService()

class PaymentCreateSchema(BaseModel):
    packId: int
    amount: float


@router.post("/payment")
def initiate_payment(payment_data: PaymentCreateSchema, db: Session = Depends(get_db), app_settings: Settings = Depends(get_settings),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    existing_subscription = db.query(Subscription).filter(
        Subscription.idPack == payment_data.packId, 
        Subscription.userId == user.user_id,
        Subscription.endDate > datetime.utcnow()  # Vérifie que la souscription n'a pas expiré
    ).first()

    if existing_subscription:
        raise HTTPException(status_code=400, detail="Vous êtes déjà souscrit à ce pack. Vous pourrez souscrire à nouveau lorsque votre souscription actuelle arrivera à expiration.")
    else:
        subscription = Subscription(idPack=payment_data.packId, 
                                userId=user.user_id, 
                                startDate=datetime.utcnow(), 
                                endDate=datetime.utcnow() + timedelta(days=365)
                                )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        subscription_id = subscription.idSubscription
    # subscription = Subscription(idPack=payment_data.packId, userId=user.user_id, startDate=datetime.utcnow(), endDate=datetime.utcnow() + timedelta(days=365))
    # db.add(subscription)
    # db.commit()
    # db.refresh(subscription)
    payment = Payment(userId=user.user_id, subscriptionId=subscription_id, amount=payment_data.amount, paymentMethod="paypal", paymentStatus="pending")
    db.add(payment)
    db.commit()
    db.refresh(payment)

    try:
        return_url = f"http://127.0.0.1:8000/api/v1/payment/execute?paymentIdDB={payment.paymentId}"
        cancel_url = f"http://127.0.0.1:8000/api/v1/payment/cancel?paymentId={payment.paymentId}"
        payment_response = paypal_service.create_payment(payment_data.amount, return_url, cancel_url)
        approval_url = next(link['href'] for link in payment_response['links'] if link['rel'] == 'approval_url')
        # Ajout de l'ID PayPal pour les utilisations futures
        return {"status": "success", "paymentUrl": approval_url, "paymentId": payment.paymentId, "paypalPaymentId": payment_response['id']}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/payment/execute")
def execute_payment(paymentIdDB: int, paymentId: str, token: str, PayerID: str, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    payment = db.query(Payment).filter(Payment.paymentId == paymentIdDB).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    try:
        # Exécution du paiement en utilisant l'ID PayPal
        payment_details = paypal_service.execute_payment(paymentId, PayerID)
        payment.paymentStatus = "completed"
        db.commit()
        return {"status": "success", "paymentDetails": payment_details}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/return")
def return_url(paymentId: int, paypalPaymentId: str, payerId: str, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ):                
               
    payment = db.query(Payment).filter(Payment.paymentId == paymentId).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    try:
        # Exécution du paiement en utilisant l'ID PayPal
        payment_details = paypal_service.execute_payment(paypalPaymentId, payerId)
        payment.paymentStatus = "completed"
        db.commit()
        return {"status": "success", "paymentDetails": payment_details}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/payment/cancel")
def cancel_payment(paymentId: int, token: str, db: Session = Depends(get_db),
    user: UserSchema = Depends(
        RoleChecker(allowed_roles=[UserType.admin, UserType.client])
        )
    ): 
    payment = db.query(Payment).filter(Payment.paymentId == paymentId).first()
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")

    try:
        # Met à jour le statut du paiement en 'canceled'
        payment.paymentStatus = "failed"
        db.commit()
        return {"status": "success", "message": "Payment was canceled"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
