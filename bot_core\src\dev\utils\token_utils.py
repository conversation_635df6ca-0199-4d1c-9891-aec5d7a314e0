from langdetect import detect
from sqlalchemy import update
from src.dev.models.bot_model import Bo<PERSON>
from src.dev.models.enums.user_type import UserType
from src.dev.utils.text_extraction import get_token_count

# Détection de la langue 
def detect_language(text: str) -> str:
    return detect(text)

# Estimation du nbr de token selon la langue
def compute_total_tokens(prompt: str, lang: str) -> int:
    if lang == "fr":
        factor = 1.3
    elif lang == "en":
        factor = 1.15
    else:
        return 0  # Si la langue n'est ni fr ni en, on retourne 0

    estimated_tokens = int(len(prompt.split()) * factor)
    return estimated_tokens

# TODO ERROR we must count the full query with context and prompte not only the question 
def update_token_counters(db, user, bot_id: int, query: str, response: str):
    if user.type != UserType.admin:
        stmt = (
            update(Bot)
            .where(Bot.idBot == bot_id)
            .values(
                currentInputToken=get_token_count(query),
                currentOutputToken=get_token_count(response)
            )
        )
        db.execute(stmt)
        db.commit()