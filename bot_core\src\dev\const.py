from enum import Enum
from typing import (
    Final,
    List,
)


# Authentication service constants
AUTH_TAGS: Final[List[str | Enum] | None] = ["Authentication"]
AUTH_URL: Final = "api/v1/auth"
TOKEN_URL: Final =f"/{AUTH_URL}/token"

TOKEN_TYPE: Final = "bearer"
ACCESS_TOKEN_EXPIRE_MINUTES: Final = 60 * 24 # 24 h 
REFRESH_TOKEN_EXPIRE_MINUTES: Final = 7 * 24 * 60 # 1 week

# Algorithm used to sign the JWT tokens
TOKEN_ALGORITHM: Final = "HS256"

# TOFIX:
TOKEN_KEY: Final = '52ca5e987db918fe48328203e972f7e1bb3f2c41b099992afaf4e1a235d7dc2c'

DATABASE_USER: str
DATABASE_PASSWORD: str
DATABASE_HOST: str
DATABASE_PORT: int
DATABASE_NAME: str



INFO_LOG = '\33[32m[INFO]\33[97m'
WAR_LOG = '\33[33m[WARN]\33[97m'
ERROR_LOG = '\33[31m[ERROR]\33[97m'