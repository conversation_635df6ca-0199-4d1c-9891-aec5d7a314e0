from pydantic import BaseModel
from datetime import date
from typing import List, Optional

# === Models ===
class TrendItem(BaseModel):
    day: date
    avg: float

class PerformanceStats(BaseModel):
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    trend_by_day: List[TrendItem]

class DailyUseItem(BaseModel):
    date: date
    count: int

class PeakHourItem(BaseModel):
    hour: int
    count: int

class UsageStats(BaseModel):
    average_daily_uses: float
    daily_trend: List[DailyUseItem]  
    peak_hours: List[PeakHourItem]
    total_questions: int
    status: str

class BotStatsResponse(BaseModel):
    performance: PerformanceStats
    usage: UsageStats