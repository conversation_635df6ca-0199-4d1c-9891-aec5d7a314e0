import json
import logging
import httpx  
import os
from dotenv import load_dotenv

from src.dev.utils.token_utils import update_token_counters
from .client_Whatsapp import send_message
from src.dev.models.user_model import User
from src.dev.utils.database import SessionLocal
from src.dev.models.question_response_model import QuestionResponseLog

#API_Key : d'après GET("api/v1/auth/bots/{botId}/api-key/new")
load_dotenv()
logger = logging.getLogger("uvicorn")
db = SessionLocal()
API_ASK_URL = os.environ["API_ASK_URL"]
API_KEY = os.environ["API_KEY"]
BOT_ID = 1

#traitement du message Whatsapp
async def process_whatsapp_message_raw(raw_body: bytes):
    try:
        data = json.loads(raw_body)
        change = data['entry'][0]['changes'][0]['value']
        
        if 'messages' not in change or 'statuses' in change:
            return
        
        # récupération du contenu du msg
        msg_obj = change['messages'][0]
        if msg_obj.get('type') != 'text':
            return

        from_number = msg_obj['from'].lstrip('+')
        message = msg_obj['text']['body']

        logger.info(f"🔵 Message WhatsApp reçu : {message} de {from_number}")

        #Vérification de user
        user = db.query(User).filter(User.phone_number == from_number).first()
        if not user:
            send_message(from_number, "❌ Vous n'êtes pas autorisé à utiliser ce bot.")
            return

        #  Appel asynchrone de askWhatsapp
        url = API_ASK_URL.format(bot_id=BOT_ID) + f"?query={httpx.QueryParams({'query': message})}"

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(url, headers={"X-API-Key": API_KEY})

        if response.status_code != 200:
            raise Exception(f"Erreur API askWhatsapp: {response.text}")

        bot_response = response.json().get("msg", "❌ Aucune réponse du bot.")
        send_message(from_number, bot_response)

        update_token_counters(db, user, BOT_ID, message, bot_response)
        
        db.add(QuestionResponseLog(id_bot=BOT_ID, user_id=user.userId, question=message, response=bot_response))
        db.commit()
    except Exception as e:
        logger.error(f"❌ Erreur : {e}")
        try:
            send_message(from_number, "⚠ Une erreur est survenue. Veuillez réessayer.")
        except Exception as e_send:
            logger.error(f"⚠ Erreur lors de l'envoi du message : {e_send}")