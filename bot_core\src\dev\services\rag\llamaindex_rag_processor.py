import os
import logging
from typing import Dict, List, Tuple, Union, Optional, Any
from dotenv import load_dotenv

from src.dev.models.Embedding_Data import EmbeddingData
from sqlalchemy.orm import Session
from fastapi import HTTPException

# Import LlamaIndex components
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.core import Document, VectorStoreIndex, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine

# Import the abstract base class
from src.dev.services.rag.rag_processor import AbstractRAGProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)

# Loading environment variables
load_dotenv()

class LlamaIndexRAGProcessor(AbstractRAGProcessor):
    def __init__(self):
        # Initialize logger first
        self.logger = logging.getLogger(__name__)
        
        try:
            # Get model name from environment or use default
            model_name = os.getenv("EMBEDDING_MODEL", "nomic-ai/nomic-embed-text-v1.5")
            
            # Initialize embedding model with trust_remote_code=True
            self.embed_model = HuggingFaceEmbedding(
                model_name=model_name,
                trust_remote_code=True  # This fixes the trust_remote_code error
            )
            
            # Set global settings
            Settings.embed_model = self.embed_model
            
            # Initialize text splitter
            self.text_splitter = SentenceSplitter(
                chunk_size=512,
                chunk_overlap=50
            )
            
            self.logger.info(f"LlamaIndexRAGProcessor initialized successfully with model: {model_name}")
            
        except Exception as e:
            self.logger.error(f"Error initializing HuggingFaceEmbedding: {e}")
            raise RuntimeError(f"Failed to initialize LlamaIndexRAGProcessor: {e}")
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess input text"""
        if not text:
            return ""
        
        # Basic text preprocessing
        text = text.strip()
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        return text
    
    def semantic_splitter(self, text: Any) -> List[str]:
        """Split text semantically into chunks"""
        try:
            if isinstance(text, str):
                preprocessed_text = self.preprocess_text(text)
                # Use LlamaIndex's SentenceSplitter
                nodes = self.text_splitter.split_text(preprocessed_text)
                return [node for node in nodes if node.strip()]
            else:
                self.logger.warning("Input text is not a string")
                return []
        except Exception as e:
            self.logger.error(f"Error in semantic splitting: {e}")
            return []
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a given text"""
        try:
            if not text or not text.strip():
                return []
            
            # Use the embedding model to generate embeddings
            embedding = self.embed_model.get_text_embedding(text.strip())
            return embedding
        except Exception as e:
            self.logger.error(f"Error generating embedding: {e}")
            return []
    
    def process_document(self, document_text: Any) -> Tuple[List[str], List[List[float]]]:
        """Process document to extract chunks and generate embeddings"""
        try:
            # Split text into chunks
            chunks = self.semantic_splitter(document_text)
            
            if not chunks:
                self.logger.warning("No chunks generated from document")
                return [], []
            
            # Generate embeddings for each chunk
            embeddings = []
            for chunk in chunks:
                embedding = self.generate_embedding(chunk)
                if embedding:
                    embeddings.append(embedding)
                else:
                    self.logger.warning(f"Failed to generate embedding for chunk: {chunk[:100]}...")
            
            self.logger.info(f"Processed document into {len(chunks)} chunks with {len(embeddings)} embeddings")
            return chunks, embeddings
            
        except Exception as e:
            self.logger.error(f"Error processing document: {e}")
            return [], []
    
    def node_embedding(self, documents: Any) -> List[List[float]]:
        """Generate embeddings for a list of documents/nodes"""
        try:
            embeddings = []
            
            if isinstance(documents, list):
                for doc in documents:
                    if isinstance(doc, str):
                        embedding = self.generate_embedding(doc)
                        if embedding:
                            embeddings.append(embedding)
                    else:
                        self.logger.warning(f"Unexpected document type: {type(doc)}")
            else:
                # Single document
                embedding = self.generate_embedding(str(documents))
                if embedding:
                    embeddings.append(embedding)
            
            return embeddings
        except Exception as e:
            self.logger.error(f"Error in node embedding: {e}")
            return []
    
    def vectorize_document_and_store_in_database(
        self, document_url: str, bot_id: int, doc_id: int, db: Session
    ) -> None:
        """Vectorize document and store in database"""
        try:
            # This would typically involve:
            # 1. Loading document from URL
            # 2. Processing it into chunks
            # 3. Generating embeddings
            # 4. Storing in database
            
            self.logger.info(f"Starting vectorization for document: {document_url}")
            
            # Placeholder implementation - you'll need to implement based on your document loading logic
            # document_text = load_document_from_url(document_url)
            # chunks, embeddings = self.process_document(document_text)
            
            # Store in database
            # for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            #     embedding_data = EmbeddingData(
            #         botId=bot_id,
            #         docId=doc_id,
            #         chunkText=chunk,
            #         embedding=embedding
            #     )
            #     db.add(embedding_data)
            
            # db.commit()
            
            self.logger.info(f"Completed vectorization for document: {document_url}")
            
        except Exception as e:
            self.logger.error(f"Error vectorizing document: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to vectorize document: {e}")
    
    def process_rag_query(
        self, prompt: str, document_key: str, db: Session
    ) -> str:
        """Process RAG query"""
        try:
            # Implement RAG query processing logic
            self.logger.info(f"Processing RAG query: {prompt[:100]}...")
            
            # This would typically involve:
            # 1. Generate query embedding
            # 2. Retrieve relevant chunks from database
            # 3. Generate response using LLM
            
            return "RAG response placeholder - implement based on your LLM integration"
            
        except Exception as e:
            self.logger.error(f"Error processing RAG query: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to process RAG query: {e}")
    
    def query_embedding(self, query: str) -> List[float]:
        """Generate embedding for a query"""
        return self.generate_embedding(query)
    
    def rerank_passages(self, query: str, passages: List[str]) -> List[str]:
        """Rerank retrieved passages based on relevance to query"""
        try:
            # Implement reranking logic
            # For now, return passages as-is
            return passages
        except Exception as e:
            self.logger.error(f"Error reranking passages: {e}")
            return passages
    
    def hybrid_retrieval(self, query: str, chunks: List[str], tokenized_corpus: List[List[str]], top_k: int = 5) -> List[str]:
        """Perform hybrid retrieval (vector + lexical)"""
        try:
            # Implement hybrid retrieval combining vector similarity and BM25
            # For now, return first top_k chunks
            return chunks[:top_k]
        except Exception as e:
            self.logger.error(f"Error in hybrid retrieval: {e}")
            return chunks[:top_k]
    
    def load_chunks_with_tokens(self, bot_id: int, db: Session) -> dict:
        """Load chunks and their tokenized versions for a specific bot"""
        try:
            self.logger.info(f"Loading chunks for bot {bot_id}")
            
            # Query the database for chunks related to this bot
            chunk_records = db.query(EmbeddingData.chunkText)\
                              .filter(EmbeddingData.botId == bot_id)\
                              .all()
            
            # Extract chunks from the database records
            chunks = [record[0] for record in chunk_records]
            
            if not chunks:
                self.logger.warning(f"No chunks found for bot {bot_id}")
                raise HTTPException(status_code=404, detail=f"No document chunks found for bot {bot_id}")
            
            self.logger.info(f"Found {len(chunks)} chunks for bot {bot_id}")
            
            # Create tokenized versions of the chunks for BM25 retrieval
            import nltk
            from nltk.tokenize import word_tokenize
            
            try:
                nltk.download('punkt', quiet=True)
            except:
                self.logger.warning("Unable to download NLTK data, proceeding with existing resources")
            
            tokenized_corpus = []
            for chunk in chunks:
                try:
                    tokens = word_tokenize(chunk.lower())
                    tokenized_corpus.append(tokens)
                except Exception as e:
                    self.logger.error(f"Error tokenizing chunk: {str(e)}")
                    tokenized_corpus.append([])
            
            return {
                "chunks": chunks,
                "tokenized_corpus": tokenized_corpus
            }
            
        except Exception as e:
            self.logger.error(f"Error loading chunks with tokens: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load chunks: {e}")
