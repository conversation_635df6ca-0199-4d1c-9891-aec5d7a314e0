import hashlib
import uuid
from datetime import datetime, timedelta
from typing import Any, Type, Union

from fastapi import Depends, HTTPException, Request, Response, status, Security
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm, APIKeyHeader
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import select, update
from sqlalchemy.orm import Session, with_polymorphic
from src.dev.const import ACCESS_TOKEN_EXPIRE_MINUTES, REFRESH_TOKEN_EXPIRE_MINUTES, \
    TOKEN_ALGORITHM, TOKEN_KEY, TOKEN_TYPE, TOKEN_URL
from src.dev.exc import raise_with_log
from src.dev.models.bot_model import Bot
from src.dev.models.enums.user_type import UserType
from src.dev.models.subscription_model import Subscription
from src.dev.models.user_model import Admin, Client, RefreshToken, User, Visitor
from src.dev.schemas.admin_schema import AdminSchema, CreateAdminSchema
from src.dev.schemas.auth import APIKeySchema, TokenSchema, UserSchema
from src.dev.schemas.client_schema import ClientSchema, CreateClientSchema
from src.dev.schemas.subscription import SubscriptionSchema
from src.dev.schemas.visitor_schema import CreateVisitorSchema, VisitorSchema
from src.dev.services.base import BaseDataManager, BaseService
from src.dev.utils.dependencies import get_db
import logging

logger = logging.getLogger("docubot.api_key")
logger.setLevel(logging.DEBUG)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_schema = OAuth2PasswordBearer(tokenUrl=TOKEN_URL, auto_error=False)
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


async def get_current_user(token: str = Depends(oauth2_schema), session: Session = Depends(get_db)):
    if not token:
        raise_with_log(status.HTTP_401_UNAUTHORIZED, "vous n'avez pas assez d'autorisations")

    try:
        # decode token using secret token key provided by config
        payload = jwt.decode(token, TOKEN_KEY, algorithms=TOKEN_ALGORITHM)

        # extract encoded information
        name: int = payload.get("name")
        sub: str = payload.get("sub")
        expires_at: str = payload.get("expires_at")

        if sub is None:
            raise_with_log(status.HTTP_401_UNAUTHORIZED, "informations d'identification invalides")

        if is_expired(expires_at):
            raise_with_log(status.HTTP_401_UNAUTHORIZED, "Token expiré")

        user = AuthDataManager(session).get_user_by_email(sub)
        if user is None:
            raise_with_log(status.HTTP_404_NOT_FOUND, "utilisateur introuvable")

        return user
    except JWTError:
        raise_with_log(status.HTTP_401_UNAUTHORIZED, "informations d'identification invalides")

    return None

async def get_current_user_or_visitor(
    request: Request,
    token: str = Depends(oauth2_schema),
    session: Session = Depends(get_db)
):
    try:
        if token:
            return await get_current_user(token, session)
    except HTTPException:
        pass

    user = get_authenticated_visitor_from_session_id(request, session)
    if user:
        return user

    raise HTTPException(status_code=401, detail="Authentication failed")

def get_authenticated_visitor_from_session_id(request: Request, db: Session = Depends(get_db)):
    session_id = request.cookies.get("session_id")

    if session_id is None:
        raise HTTPException(
            status_code=401,
            detail="session ID n'est pas trouvé dans les cookies.",
        )
    user = AuthDataManager(db).get_visitor_from_session(session_id)
    return user

def is_expired(expires_at: str) -> bool:
    return datetime.strptime(expires_at, "%Y-%m-%d %H:%M:%S") < datetime.now()


async def api_key_security(
    bot_id: int,
    session: Session = Depends(get_db),
    api_key: str = Security(api_key_header),
    user: UserSchema = Depends(get_current_user_or_visitor),
):
    
    if user.type == UserType.admin:
        return None
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="une clé API doit être transmise en tant qu'en-tête (Header)",
        )
    
    try:
        if not ApiKeyService(session).check_api_key(api_key, bot_id):
            raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="clé API expirée ou limite atteinte"
        )
    except:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="clé API erronée ou révoquée."
        )
    
    return api_key




class HashingMixin:
    @staticmethod
    def get_password_hash(password: str) -> str:
        return pwd_context.hash(password)

    @staticmethod
    def verify_password(hashed_password: str, plain_password: str) -> bool:
        return pwd_context.verify(plain_password, hashed_password)
    
class AuthService(HashingMixin, BaseService):
    def authenticate(
        self, login: OAuth2PasswordRequestForm = Depends()
    ) -> TokenSchema | None:
        user = AuthDataManager(self.session).get_user_by_email(login.username)

        if user.type == UserType.visitor:
            raise_with_log(status.HTTP_401_UNAUTHORIZED, "Vous ne disposez pas des droits nécessaires pour sign in")
        
        elif user.hashed_password is None:
            raise_with_log(status.HTTP_401_UNAUTHORIZED, "mot de passe incorrect")
        else:
            if not self.verify_password(user.hashed_password, login.password):
                raise_with_log(status.HTTP_401_UNAUTHORIZED, "mot de passe incorrect")
            else:
                refresh_token_db =  AuthDataManager(self.session).get_refresh_token(user.user_id)
                if refresh_token_db:
                    AuthDataManager(self.session).delete_refresh_token(user.user_id)
                
                access_token = self._create_access_token(user.username, user.email)
                refresh_token = self._create_refresh_token(user.username, user.email)

                refresh_token_model = RefreshToken(user_id=user.user_id, 
                                                    refresh_token=refresh_token)
                AuthDataManager(self.session).add_refresh_token(refresh_token_model)
                
                return TokenSchema(access_token=access_token, 
                                   refresh_token=refresh_token, 
                                   token_type=TOKEN_TYPE)
        return None
    
    def _create_access_token(self, name: str, email: str) -> str:
        payload = {
            "name": name,
            "sub": email,
            "expires_at": self._expiration_time(expries_at=ACCESS_TOKEN_EXPIRE_MINUTES),
        }

        return jwt.encode(payload, TOKEN_KEY, algorithm= TOKEN_ALGORITHM)
    
    def _create_refresh_token(self, name: str, email: str) -> str:
        payload = {
            "name": name,
            "sub": email,
            "expires_at": self._expiration_time(expries_at=REFRESH_TOKEN_EXPIRE_MINUTES),
        }

        return jwt.encode(payload, TOKEN_KEY, algorithm= TOKEN_ALGORITHM)
    
    @staticmethod
    def _expiration_time(expries_at:int) -> str:
        """Get token expiration time."""

        expires_at = datetime.now() + timedelta(minutes=expries_at)
        return expires_at.strftime("%Y-%m-%d %H:%M:%S")

    def _verify_refresh_token(self, tokenStr: str):
        try:
            payload = jwt.decode(tokenStr, TOKEN_KEY, algorithms=TOKEN_ALGORITHM)

            name: int = payload.get("name")
            sub: str = payload.get("sub")
            expires_at: str = payload.get("expires_at")

            if sub is None:
                raise_with_log(status.HTTP_401_UNAUTHORIZED, "informations d'identification invalides")

            if is_expired(expires_at):
                raise_with_log(status.HTTP_401_UNAUTHORIZED, "Refresh Token a expiré, vous devez vous connecter")

            return name, sub
        except JWTError:
            raise_with_log(status.HTTP_401_UNAUTHORIZED, "informations d'identification invalides")

        return None

    def get_new_access_token(self, tokenStr: str):
        username, email = self._verify_refresh_token(tokenStr)
        new_access_token = self._create_access_token(username, email)
        return TokenSchema(access_token=new_access_token, 
                           refresh_token=tokenStr, 
                           token_type=TOKEN_TYPE)
    
    def generate_session_id(self):
        return str(uuid.uuid4())
    
    def uuid_to_integer(self, uuid_str):
        hash_object = hashlib.md5(uuid_str.encode())
        hex_dig = hash_object.hexdigest()
        return int(hex_dig, 16) % (10**8) 

    def create_account(self, user:  Type[Union[AdminSchema, ClientSchema ,VisitorSchema]]) -> AdminSchema | ClientSchema | VisitorSchema:
        AuthDataManager(self.session).is_user_exist(user.email)
        hashed_password = self.get_password_hash(user.password)

        schema_kwargs = {
            "userName": user.username,
            "password": hashed_password,
            "email": user.email,
            "createDate": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "lastLogin": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "additionalInfo": user.additional_info
        }

        if isinstance(user, CreateAdminSchema): 
            schema_kwargs["type"] = UserType.admin
            model =  Admin(**schema_kwargs)
        elif isinstance(user, CreateVisitorSchema):
            schema_kwargs["type"] = UserType.visitor
            model =  Visitor(**schema_kwargs)
        elif isinstance(user, CreateClientSchema):
            schema_kwargs["type"] = UserType.client
            schema_kwargs["companyName"] = user.companyName 
            schema_kwargs["adminUserId"] = user.adminUserId 
            model =  Client(**schema_kwargs)
        else:
            raise ValueError("type d'utilisateur non valide")

        AuthDataManager(self.session).add_user(model)
        user_db = AuthDataManager(self.session).get_user_by_email(user.email)
        return user_db
    
    def login_visitor(self, email: str,response: Response):
        visitor = AuthDataManager(self.session).get_user_by_email(email)
        if not isinstance(visitor, VisitorSchema):
            raise_with_log(status.HTTP_404_NOT_FOUND, "visiteur non trouve")

        session_id = self.uuid_to_integer(self.generate_session_id())
        AuthDataManager(self.session).update_visitor_session(visitor, session_id)
        response.set_cookie(
            key="session_id", 
            value=session_id,
            #samesite="none",
            #secure=True,
            path="/",
            # httponly=True
        )

        return {"message": "Email reçu et traité avec succès", "session_id": session_id}



        

class ApiKeyService(BaseService):
    def create_api_key(self, user: UserSchema, id_bot: int) -> str:
        api_key = str(uuid.uuid4())
        AuthDataManager(self.session).save_api_key(user, id_bot, api_key)
        return api_key
    

    def check_api_key(self, api_key: str, bot_id: int) -> bool:
        logger.debug(f"Début de vérification de la clé API : {api_key} pour bot_id : {bot_id}")

        try:
            apikey_info = AuthDataManager(self.session).get_apikey_info(api_key)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des infos de la clé API : {e}")
            return False
        
        if not apikey_info:
            logger.warning("Clé API non trouvée.")
            return False

        logger.debug(f"Clé API trouvée. ID bot attendu : {apikey_info.id_bot}")

        if apikey_info.id_bot != bot_id:
            logger.warning(f"Bot ID incorrect. Attendu : {apikey_info.id_bot}, reçu : {bot_id}")
            return False

        if not self._is_expired(apikey_info):
            logger.debug("Clé API non expirée.")
            if not self._limit_reached(apikey_info):
                logger.debug("Limite d’utilisation non atteinte. Clé API valide.")
                return True        
            else:
                logger.warning("Limite d’utilisation atteinte pour la clé API.")
        else:
            logger.warning("Clé API expirée.")

        logger.info("Clé API invalide (expiration ou limite atteinte).")

        return False
        
    def  _is_expired(self, apikey_info: APIKeySchema)-> bool:
        logger.debug(
                f"[Vérification expiration API key] "
                f"Start: {apikey_info.start}, End: {apikey_info.end}, Now: {datetime.now() }, Expired: {not (apikey_info.start <= datetime.now() <= apikey_info.end)}"
            )
        return not (apikey_info.start <= datetime.now() <= apikey_info.end)

    def _limit_reached(self, apikey_info: APIKeySchema)-> bool:
        return apikey_info.is_limit_reached

    def revoke_key(self, user: UserSchema, id_bot: int) -> None:
        api_key = None
        AuthDataManager(self.session).save_api_key(user, id_bot, api_key)
        


class AuthDataManager(BaseDataManager):
    def add_user(self, user: User) -> None:
        self.add_one(user)

    def update_visitor_session(self, visitor: VisitorSchema, session_id: int) -> None:
        stmt = (
        update(Visitor).where(Visitor.userId == visitor.user_id)
        .values(sessionId=session_id)
        )
        self.update_one(stmt)

    def get_user_by_email(self, email: str) -> AdminSchema | ClientSchema | VisitorSchema :
        UserPolymorphic = with_polymorphic(User, [Admin, Client, Visitor])
        stmt = select(UserPolymorphic).where(UserPolymorphic.email == email)
        model = self.session.execute(stmt).scalars().first()

        if not model:
            raise_with_log(status.HTTP_404_NOT_FOUND, f"utilisateur non trouvé")
        
        schema_kwargs = {
            "user_id": model.userId,
            "username": model.userName,
            "email": model.email,
            "additional_info": model.additionalInfo,
            "type": model.type,
            "hashed_password": model.password,
        }

        if isinstance(model, Visitor):
            return VisitorSchema(**schema_kwargs, session_id=model.sessionId)
        elif isinstance(model, Admin):
            return AdminSchema(**schema_kwargs)
        elif isinstance(model, Client):
            return ClientSchema(**schema_kwargs, companyName=model.companyName, adminUserId=model.adminUserId)
        

    def get_visitor_from_session(self, session_id: int):
        VisitorPolymorphic = with_polymorphic(User, [Visitor])
        visitor = self.session.query(VisitorPolymorphic).filter(Visitor.sessionId == session_id).first()
        if not visitor:
            raise HTTPException(status_code=404, detail="visiteur non trouvé dans la base de donnees")
        
        return VisitorSchema(
            user_id= visitor.userId,
            username= visitor.userName,
            email= visitor.email,
            hashed_password= visitor.password,
            type= visitor.type,
            additional_info= visitor.additionalInfo,
            session_id= visitor.sessionId
        )


    def is_user_exist(self, email: str)-> Any:
        stmt = select(User).where(User.email == email)
        model =  self.get_one(stmt)

        if model:
            raise_with_log(status.HTTP_409_CONFLICT, "cette adresse e-mail est déjà utilisée")

    def get_refresh_token(self, user_id: int):
        stmt = select(RefreshToken).where(RefreshToken.userId == user_id)
        model =  self.get_one(stmt)

        return model 
    
    def delete_refresh_token(self, user_id: int):
        stmt = select(RefreshToken).where(RefreshToken.userId == user_id)
        self.delete_one(stmt)

    def add_refresh_token(self, refresh_token: RefreshToken):
        self.add_one(refresh_token)

    def save_api_key(self,user: UserSchema, id_bot: int, api_key: str):
        if user.type != UserType.admin:
            user_id = user.user_id  
            bots  = self.get_user_bots(user_id)

            bot_ids = map(lambda bot: bot.idBot, bots)          
            if id_bot not in list(bot_ids):
                raise_with_log(status.HTTP_404_NOT_FOUND, "Vous ne disposez pas des droits nécessaires pour créer une clé API pour ce bot")

        stmt = (
        update(Bot).where(Bot.idBot == id_bot)
        .values(apiKey=api_key)
        )
        self.update_one(update_stmt=stmt)

    def get_apikey_info(self, api_key: str)-> APIKeySchema:
        stmt = select(Bot).where(Bot.apiKey == api_key)
        model =  self.get_one(stmt)
        if not isinstance(model, Bot):
            raise_with_log(status.HTTP_404_NOT_FOUND, "vous avez pas un clé API")

        id_sub = model.idSubscription
        subscription_info = self.get_subscription_info(id_sub)
        
        return APIKeySchema(api_key=api_key, 
                            id_bot= model.idBot, 
                            start=subscription_info.start_date, 
                            end=subscription_info.end_date, 
                            is_limit_reached=model.isLimitReached) 
        

    def get_subscription_info(self, id_sub: int) -> SubscriptionSchema:
        stmt = select(Subscription).where(Subscription.idSubscription == id_sub)
        model =  self.get_one(stmt)
        if not isinstance(model, Subscription):
            raise_with_log(status.HTTP_404_NOT_FOUND, "l'inscription que vous recherchez est introuvable")
       
        return SubscriptionSchema(
            id_subscription=model.idSubscription,
            id_pack=model.idPack,
            id_user=model.userId,
            start_date=model.startDate,
            end_date=model.endDate,
            status=model.status,
        )

    def get_user_bots(self, user_id: int):
        # Sélectionner les bots associés à l'utilisateur donné
        stmt = (
            select(Bot)
            .join(Subscription, Subscription.idSubscription == Bot.idSubscription)
            .join(User, User.userId == Subscription.userId)
            .where(User.userId == user_id)
        )

        bots = self.get_all(stmt)
        if not bots:
            raise_with_log(status.HTTP_404_NOT_FOUND, "Aucun bot trouvé pour l'utilisateur connecté")

        return bots


