import os
import json
import logging
import re


from fastapi import HTTPException
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.documentation_model import Documentation, DocumentTypeEnum
from src.dev.api.controllers.ClientController import ClientController
from src.dev.services.rag.rag_processor_factory import get_rag_processor

from src.dev.services.rag.chunking_utils import get_smart_text_end_improved

from src.dev.services.rag.chunking_utils import get_smart_text_start_improved

from src.dev.services.rag.chunking_utils import find_sentence_continuation

from src.dev.services.rag.chunking_utils import find_concept_bridge

from src.dev.services.rag.chunking_utils import extract_sentences_with_words

from src.dev.services.rag.document_processing_utils import deduplicate_chunks

from src.dev.services.rag.document_processing_utils import verify_page_coverage

from src.dev.services.rag.text_utils import strip_nul, remove_binary_garbage



logger = logging.getLogger("uvicorn")
def _process_content_for_chunks(content: str, processor, page_num: int, chunks_list: list, embeddings_list: list, min_chunk_size: int | None = None):
    """
    Traite le contenu donné pour générer des chunks et des embeddings, et les ajoute aux listes fournies.
    Gère la logique de chunking basée sur min_chunk_size ou le chunking normal.
    """
    try:
        if min_chunk_size is not None and len(content) < min_chunk_size:
            # Cas de page courte : créer un chunk unique
            # Utiliser process_document au lieu de embed_text
            page_chunks, page_embeddings = processor.process_document(content)
            if page_chunks and page_embeddings:
                # Prendre le premier chunk/embedding ou combiner si multiple
                chunk_text = page_chunks[0] if not isinstance(page_chunks[0], tuple) else page_chunks[0][0]
                chunks_list.append((page_num, str(chunk_text)))
                embeddings_list.append(page_embeddings[0])
                logger.info(f"Page {page_num}: Single chunk from short content ({len(content)} chars)")
            else:
                logger.warning(f"Page {page_num}: No chunks generated from short content")
        else:
            # Chunking normal
            page_chunks, page_embeddings = processor.process_document(content)
            if page_chunks and page_embeddings:
                for chunk, embedding in zip(page_chunks, page_embeddings):
                    chunk_text = chunk if not isinstance(chunk, tuple) else chunk[0]
                    chunks_list.append((page_num, str(chunk_text)))
                    embeddings_list.append(embedding)
                logger.info(f"Page {page_num}: {len(page_chunks)} chunks")
            else:
                logger.warning(f"Page {page_num}: No chunks generated from content")
    except Exception as e:
        logger.error(f"Error processing content for page {page_num}: {str(e)}")
        # Optionnel : essayer un chunking de base en cas d'erreur
        try:
            if content.strip():
                # Chunking basique en cas d'erreur
                basic_chunks = [content[i:i+2000] for i in range(0, len(content), 1500)]
                for i, chunk in enumerate(basic_chunks):
                    if chunk.strip():
                        # Essayer de créer un embedding basique
                        try:
                            fallback_chunks, fallback_embeddings = processor.process_document(chunk)
                            if fallback_chunks and fallback_embeddings:
                                chunks_list.append((page_num, str(fallback_chunks[0])))
                                embeddings_list.append(fallback_embeddings[0])
                        except:
                            # Si même le fallback échoue, ignorer ce chunk
                            pass
        except Exception as fallback_error:
            logger.error(f"Fallback chunking failed for page {page_num}: {str(fallback_error)}")


def create_page_chunks_with_context(docs_with_pages, processor, overlap_size, min_chunk_size):
    """
    Crée des chunks par page avec contexte des pages adjacentes.
    Version corrigée avec meilleure gestion d'erreurs.
    """
    chunks = []
    embeddings = []

    for i, (page_num, page_content) in enumerate(docs_with_pages):
        try:
            clean_content = remove_binary_garbage(page_content)
            if not clean_content.strip():
                logger.warning(f"Page {page_num}: Empty content after cleaning")
                continue

            # Construire le contenu avec contexte
            extended_content = build_page_with_context(docs_with_pages, i, overlap_size)

            # Vérifier que le contenu étendu n'est pas vide
            if not extended_content.strip():
                logger.warning(f"Page {page_num}: Empty extended content")
                continue

            _process_content_for_chunks(extended_content, processor, page_num, chunks, embeddings, min_chunk_size)

        except Exception as e:
            logger.error(f"Error processing page {page_num}: {str(e)}")
            # Essayer de traiter la page sans contexte
            try:
                clean_content = remove_binary_garbage(page_content)
                if clean_content.strip():
                    _process_content_for_chunks(clean_content, processor, page_num, chunks, embeddings, min_chunk_size)
            except Exception as fallback_error:
                logger.error(f"Fallback failed for page {page_num}: {str(fallback_error)}")
                continue

    return {"chunks": chunks, "embeddings": embeddings}


def create_intelligent_transition_chunks(docs_with_pages, processor, overlap_size):
    """
    Crée des chunks de transition intelligents qui préservent la continuité.
    Version corrigée avec meilleure gestion d'erreurs.
    """
    chunks = []
    embeddings = []

    for i in range(len(docs_with_pages) - 1):
        try:
            current_page_num, current_content = docs_with_pages[i]
            next_page_num, next_content = docs_with_pages[i + 1]

            current_clean = remove_binary_garbage(current_content)
            next_clean = remove_binary_garbage(next_content)

            # Vérifier que les contenus ne sont pas vides
            if not current_clean.strip() or not next_clean.strip():
                logger.warning(f"Skipping transition {current_page_num}→{next_page_num}: empty content")
                continue

            # Créer plusieurs types de transitions
            transition_variants = create_transition_variants(
                current_clean, next_clean, current_page_num, next_page_num, overlap_size
            )

            for variant_content, target_page in transition_variants:
                if len(variant_content.strip()) > 50:  # Minimum de contenu
                    try:
                        _process_content_for_chunks(variant_content, processor, target_page, chunks, embeddings)
                    except Exception as chunk_error:
                        logger.warning(f"Error processing transition chunk {current_page_num}→{next_page_num}: {str(chunk_error)}")
                        continue

        except Exception as e:
            logger.error(f"Error processing transition between pages {i+1} and {i+2}: {str(e)}")
            continue

    logger.info(f"Created {len(chunks)} transition chunks")
    return {"chunks": chunks, "embeddings": embeddings}


def vectorize_and_store_document(bot_id: int, document_id: int, db, force=False, overlap_size=300, min_chunk_size=100):
    """
    Version corrigée avec meilleure gestion d'erreurs et validation.
    """
    document = db.query(Documentation).filter_by(idDocumentation=document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    if document.embedded and not force:
        logger.info("Document already embedded, skipping.")
        return {"message": "Document already embedded", "document_embedded": True}

    source = document.url
    doc_type = document.type

    if not source or not os.path.exists(source):
        raise HTTPException(status_code=404, detail=f"File not found at path: {source}")

    # Lecture du document
    try:
        docs = ClientController().get_document_reader(document_type=doc_type, source=source)
        docs_with_pages = extract_pages_content(docs, doc_type, source)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Could not read document: {str(e)}")

    # Validation du contenu
    if not docs_with_pages:
        raise HTTPException(status_code=400, detail="No pages extracted from document")

    total_content = "\n\n".join([content for _, content in docs_with_pages])
    if not total_content.strip():
        raise HTTPException(status_code=400, detail="No content extracted from document")

    logger.info(f"Content length: {len(total_content)} characters from {len(docs_with_pages)} page(s)")

    processor = get_rag_processor()
    all_chunks = []
    all_embeddings = []

    # STRATÉGIE 1: Chunks par page avec contexte
    try:
        page_chunks_data = create_page_chunks_with_context(docs_with_pages, processor, overlap_size, min_chunk_size)
        all_chunks.extend(page_chunks_data['chunks'])
        all_embeddings.extend(page_chunks_data['embeddings'])
        logger.info(f"Page chunks: {len(page_chunks_data['chunks'])}")
    except Exception as e:
        logger.error(f"Error in page chunking: {str(e)}")

    # STRATÉGIE 2: Chunks de transition pour préserver la continuité
    try:
        transition_chunks_data = create_intelligent_transition_chunks(docs_with_pages, processor, overlap_size)
        all_chunks.extend(transition_chunks_data['chunks'])
        all_embeddings.extend(transition_chunks_data['embeddings'])
        logger.info(f"Transition chunks: {len(transition_chunks_data['chunks'])}")
    except Exception as e:
        logger.error(f"Error in transition chunking: {str(e)}")

    # Déduplication des chunks similaires
    try:
        unique_chunks, unique_embeddings = deduplicate_chunks(all_chunks, all_embeddings)
        logger.info(f"Generated {len(all_chunks)} chunks, {len(unique_chunks)} after deduplication")
    except Exception as e:
        logger.error(f"Error in deduplication: {str(e)}")
        # Utiliser les chunks originaux si la déduplication échoue
        unique_chunks, unique_embeddings = all_chunks, all_embeddings

    # Vérification de couverture
    try:
        coverage_report = verify_page_coverage(unique_chunks, docs_with_pages)
        logger.info(f"Page coverage: {coverage_report}")
    except Exception as e:
        logger.error(f"Error in coverage verification: {str(e)}")
        coverage_report = {"error": str(e)}

    # Stockage
    try:
        embeddings_count = store_embeddings(unique_chunks, unique_embeddings, bot_id, document_id, source, processor, db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error storing embeddings: {str(e)}")

    # Finalisation
    document.embedded = True
    db.commit()
    db.refresh(document)

    strategies_used = ["page_chunks", "transition_chunks"]
    if len(docs_with_pages) > 5:
        strategies_used.append("semantic_chunks")

    return {
        "chunks": len(unique_chunks),
        "embeddings": embeddings_count,
        "document_embedded": document.embedded,
        "pages_processed": len(docs_with_pages),
        "coverage_report": coverage_report,
        "strategies_used": strategies_used
    }
def create_page_chunks_with_context(docs_with_pages, processor, overlap_size, min_chunk_size):
    """
    Crée des chunks par page avec contexte des pages adjacentes.
    """
    chunks = []
    embeddings = []

    for i, (page_num, page_content) in enumerate(docs_with_pages):
        clean_content = remove_binary_garbage(page_content)
        if not clean_content.strip():
            continue

        # Construire le contenu avec contexte
        extended_content = build_page_with_context(docs_with_pages, i, overlap_size)

        _process_content_for_chunks(extended_content, processor, page_num, chunks, embeddings, min_chunk_size)

    return {"chunks": chunks, "embeddings": embeddings}



def create_transition_variants(current_content, next_content, current_page, next_page, overlap_size):
    """
    Crée différentes variantes de chunks de transition.
    """
    variants = []

    # Variante 1: Fin de page courante + début de page suivante
    current_end = get_smart_text_end_improved(current_content, overlap_size)
    next_start = get_smart_text_start_improved(next_content, overlap_size)

    if current_end and next_start:
        transition1 = f"{current_end}\n[PAGE_TRANSITION {current_page}→{next_page}]\n{next_start}"
        variants.append((transition1, current_page))

    # Variante 2: Recherche de phrases qui se continuent
    sentence_continuation = find_sentence_continuation(current_content, next_content)
    if sentence_continuation:
        variants.append((sentence_continuation, current_page))

    # Variante 3: Recherche de concepts liés
    concept_bridge = find_concept_bridge(current_content, next_content, overlap_size)
    if concept_bridge:
        variants.append((concept_bridge, next_page))

    return variants

def build_page_with_context(docs_with_pages, page_index, overlap_size):
    """
    Construit le contenu d'une page avec contexte des pages adjacentes.
    """
    page_num, page_content = docs_with_pages[page_index]
    clean_content = remove_binary_garbage(page_content)

    extended_content = clean_content

    # Contexte de la page précédente
    if page_index > 0:
        prev_content = remove_binary_garbage(docs_with_pages[page_index - 1][1])
        prev_context = get_smart_text_end_improved(prev_content, overlap_size)
        if prev_context:
            extended_content = f"{prev_context}\n[CONTEXT_FROM_PREV_PAGE]\n{extended_content}"

    # Contexte de la page suivante
    if page_index < len(docs_with_pages) - 1:
        next_content = remove_binary_garbage(docs_with_pages[page_index + 1][1])
        next_context = get_smart_text_start_improved(next_content, overlap_size)
        if next_context:
            extended_content = f"{extended_content}\n[CONTEXT_TO_NEXT_PAGE]\n{next_context}"

    return extended_content


def extract_pages_content(docs, doc_type, source):
    """
    Extrait le contenu des pages depuis le document reader.
    """
    if isinstance(docs, list) and len(docs) > 0:
        docs_with_pages = []
        for i, doc in enumerate(docs):
            if hasattr(doc, 'text'):
                page_num = getattr(doc, 'page', i + 1)
                docs_with_pages.append((page_num, doc.text))
            else:
                page_num = getattr(doc, 'page', i + 1)
                content = str(doc)
                docs_with_pages.append((page_num, content))
        return docs_with_pages
    elif docs:
        return [(1, str(docs))]
    else:
        if doc_type == DocumentTypeEnum.TYPE_DOC_FILE.value:
            try:
                with open(source, 'r', encoding='utf-8') as f:
                    document_content = f.read()
                return [(1, document_content)]
            except UnicodeDecodeError:
                with open(source, 'r', encoding='latin-1') as f:
                    document_content = f.read()
                return [(1, document_content)]
        else:
            raise HTTPException(status_code=400, detail="No content could be extracted from document")


def store_embeddings(chunks, embeddings, bot_id, document_id, source, processor, db):
    """
    Stocke les embeddings en base de données.
    """
    existing_chunks = set(c[0] for c in db.query(EmbeddingData.chunkText).filter_by(botId=bot_id, docId=document_id).all())
    embeddings_count = 0

    for (page_num, chunk_text), embedding in zip(chunks, embeddings):
        clean_chunk = strip_nul(chunk_text)
        if clean_chunk and clean_chunk not in existing_chunks:
            try:
                tokens = processor._tokenize_chunk(clean_chunk)
                embed = EmbeddingData(
                    botId=bot_id,
                    docId=document_id,
                    chunkText=clean_chunk,
                    embeddings=embedding,
                    pageNumber=page_num,
                    tokenized_tokens=strip_nul(json.dumps(tokens)),
                    docUrl=source
                )
                db.add(embed)
                embeddings_count += 1
            except Exception as e:
                logger.warning(f"Error processing chunk from page {page_num}: {str(e)}")
                continue

    if embeddings_count > 0:
        try:
            db.commit()
        except ValueError as e:
            if "NUL" in str(e):
                db.rollback()
                raise
            raise

    return embeddings_count


