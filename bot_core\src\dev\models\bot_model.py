import enum
from sqlalchemy import  Column, DateTime, Enum, Integer, String, ForeignKey, Text, func
from src.dev.utils.database import Base
from sqlalchemy.orm import relationship

class BotStatus(str, enum.Enum):
    active = "active"
    blocked = "blocked"

class Bot(Base):
    __tablename__ = 'bot'

    idBot = Column(Integer, primary_key=True, index=True, autoincrement=True)
    idSubscription = Column(Integer, nullable=False)
    botName = Column(String(50), nullable=True)
    applicationName = Column(String(50), nullable=True)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now())
    status = Column(Enum(BotStatus), default=BotStatus.active)
    currentInputToken = Column(Integer, nullable=True)
    currentOutputToken = Column(Integer, nullable=True)
    isLimitReached = Column(Integer, nullable=True)
    apiKey = Column(Text, unique=True, default=None)


    # Relationships
    bot_includes = relationship("BotIncludeDocumentation", back_populates="bot")

    # Relationship with the Subscription model (assuming you have one defined similarly)
    # subscription = relationship("Subscription", back_populates="bots")

    ai_model = "LLAMA"  # add this to database

    # token_usage_logs = relationship("TokenUsageLog", back_populates="bot")

    questionResponseLogs = relationship("QuestionResponseLog", back_populates="bot")

    # bot_includes = relationship("BotIncludeDocumentation", back_populates="bot")


#    rag_settings_id = Column(Integer, ForeignKey('ragsettings.id'))

class RAGSettings():
#    __tablename__ = 'ragsettings'
#    id = Column(Integer, primary_key=True, index=True)
#   document_index = Column(String)  # Could reference an Elasticsearch index
    # Add more settings as required
    temperature = 1
    max_tokens = 100

# You can define more detailed settings or configurations as needed