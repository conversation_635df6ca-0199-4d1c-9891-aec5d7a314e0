import requests
from src.dev.config.config import Settings,get_settings
from fastapi import APIRouter, Depends, Query, HTTPException
# from src.dev.config.config import PAYPAL_CLIENT_ID, PAYPAL_CLIENT_SECRET, PAYPAL_API_URL


settings = get_settings()

class PayPalService:
    def __init__(self):
        self.client_id = settings.PAYPAL_CLIENT_ID
        self.client_secret = settings.PAYPAL_CLIENT_SECRET
        self.api_url = settings.PAYPAL_API_URL

    def get_access_token(self):
        response = requests.post(f"{self.api_url}/v1/oauth2/token",
                                 headers={"Accept": "application/json", "Accept-Language": "en_US"},
                                 auth=(self.client_id, self.client_secret),
                                 data={"grant_type": "client_credentials"})
        response.raise_for_status()
        return response.json()['access_token']

    def create_payment(self, amount, return_url, cancel_url):
        access_token = self.get_access_token()
        payload = {
            "intent": "sale",
            "payer": {"payment_method": "paypal"},
            "transactions": [{
                "amount": {"total": f"{amount:.2f}", "currency": "USD"},
                "description": "Payment description"
            }],
            "redirect_urls": {
                "return_url": return_url,
                "cancel_url": cancel_url
            }
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}"
        }
        response = requests.post(f"{self.api_url}/v1/payments/payment", json=payload, headers=headers)
        response.raise_for_status()
        return response.json()

    def execute_payment(self, payment_id, payer_id):
        access_token = self.get_access_token()
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}"
        }
        response = requests.post(f"{self.api_url}/v1/payments/payment/{payment_id}/execute",
                                 json={"payer_id": payer_id}, headers=headers)
        response.raise_for_status()
        return response.json()