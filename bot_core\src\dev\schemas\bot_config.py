
from src.dev.schemas.base import BaseSchema


class AditionalSettings(BaseSchema):
  language: str = 'francais'
  responseFormat: str | None = 'text'
  maxAllowedInputTokens: int = 1000 
  includesource: bool = False
  accesssource: bool = False


class BotConf(BaseSchema):
  botId: int
  botName: str
  apiToken: str | None
  applicationName: str
  # TODO: you may need to change this ↓ 
  apiBaseUrl: str  = 'http://localhost:8000' 
  additionalSettings: AditionalSettings = AditionalSettings()
 



