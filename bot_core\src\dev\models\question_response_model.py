from sqlalchemy import Column, Integer, Text, DateTime, <PERSON>ole<PERSON>, String, ForeignKey, Enum, Interval
from sqlalchemy.orm import relationship, Mapped, mapped_column
from src.dev.utils.enums import ResponseSourceEnum
from src.dev.utils.database import Base
from src.dev.models.bot_model import Bot
from src.dev.models.user_model import User
from datetime import datetime
from sqlalchemy import Sequence
import enum
from sqlalchemy.dialects.postgresql import ENUM
from typing import cast


class FeedbackStatus(str, enum.Enum):
    INSATISFAISANTE = "1"
    REVISEE = "2"
    TRAITEE = "3"
    SUPPRIMEE = "4"


class PriorityEnum(str, enum.Enum):
    low = "low"
    medium = "medium"
    high = "high"

class QuestionResponseLog(Base):
    __tablename__ = 'questionreponselog'
    
    # Ensure the primary key matches your database exactly
    idLog = Column(Integer, Sequence('questionreponselog_idLog_seq'), primary_key=True, index=True, autoincrement=True)
    idBot = Column(Integer, ForeignKey('bot.idBot'), nullable=False, index=True)
    userId = Column(Integer, ForeignKey('user.userId'), nullable=False, index=True)
    question = Column(Text, nullable=True)
    response = Column(Text, nullable=True)
    langue = Column(String(10), default="fr", nullable=False)
    # Match your DB schema exactly
    timeQuestion = Column(DateTime(timezone=False), nullable=False, index=True)
    feedback = Column(Integer,nullable=False,default=0)
    feedbackText = Column(String(254), nullable=True)
    conversationId = Column(String(100), nullable=True, index=True)
    escalated = Column(Boolean, default=False, nullable=False)
    escalatedReason = Column(Text, nullable=True)
    responseTime = Column(Interval, nullable=True)
    
    # Match DB schema exactly
    responseSource = Column(
        ENUM(ResponseSourceEnum, name="responsesourceenum", create_type=True), 
        nullable=False, 
        default=ResponseSourceEnum.ia
        )
    priority = Column(
        ENUM(PriorityEnum, name="priorityenum", create_type=True), 
        nullable=False, 
        default=PriorityEnum.medium
        )
    agentUserId = Column(Integer, ForeignKey('agent.userId'), nullable=True)
    
    # Relations - make sure these are properly defined in your Bot and User models
    bot = relationship("Bot", back_populates="questionResponseLogs")
    user = relationship("User", back_populates="questionResponseLogs")
    
    # Add agent relationship if you have an Agent model
    agent = relationship("Agent", foreign_keys=[agentUserId], back_populates="questionResponseLogs")
    
    def __init__(self, id_bot, user_id, question, response, timeQuestion: datetime = datetime.utcnow(),
                 feedback: int | None = None, 
                 feedback_text: str | None = None,
                 response_source: ResponseSourceEnum = ResponseSourceEnum.ia,
                 conversation_id: str | None = None,
                 escalated: bool = False,
                 escalated_reason: str | None = None,
                 response_time = None,
                 priority: PriorityEnum = PriorityEnum.medium,
                 agent_user_id: int | None = None,
                 langue: str = "fr"):
        self.idBot = id_bot
        self.userId = user_id
        self.question = question
        self.response = response
        self.langue = langue
        
        # Handle datetime properly
        if timeQuestion:
            if timeQuestion.tzinfo:
                self.timeQuestion = timeQuestion.replace(tzinfo=None)
            else:
                self.timeQuestion = timeQuestion
        else:
            self.timeQuestion = datetime.utcnow()
        
        # Handle feedback
        if isinstance(feedback, FeedbackStatus):
            self.feedback = int(feedback.value)
        elif isinstance(feedback, int):
            self.feedback = feedback
        else:
            self.feedback = None  # Allow null values as per DB schema
            
        self.feedbackText = feedback_text
        
        # Handle enums
        if isinstance(response_source, str):
            self.responseSource = ResponseSourceEnum(response_source)  # convert string to Enum
        else:
            self.responseSource = response_source  # already an Enum

        self.priority = priority.value if isinstance(priority, PriorityEnum) else priority
        
        self.conversationId = conversation_id
        self.escalated = escalated
        self.escalatedReason = escalated_reason
        self.responseTime = response_time
        self.agentUserId = agent_user_id
    


    def to_dict(self):
        """Convert model instance to dictionary for API responses"""
        return {
            'idLog': self.idLog,
            'idBot': self.idBot,
            'userId': self.userId,
            'question': self.question,
            'response': self.response,
            'langue': self.langue,
            'timeQuestion': self.timeQuestion.isoformat() if self.timeQuestion else None,
            'feedback': self.feedback,
            'feedbackText': self.feedbackText,
            'conversationId': self.conversationId,
            'escalated': self.escalated,
            'escalatedReason': self.escalatedReason,
            'responseTime': str(self.responseTime) if self.responseTime else None,
            'responseSource': self.responseSource,
            'priority': self.priority,
            'agentUserId': self.agentUserId
        }
