from enum import Enum

class DocumentTypeEnum(str, Enum):
    TYPE_DOC_FILE = "file"          # Fichiers génériques
    TYPE_DOC_HTML_PAGE = "html_page"  # Pages HTML
    TYPE_DOC_PDF = "pdf"            # Documents PDF
    TYPE_DOC_EXCEL = "excel"        # Fichiers Excel (.xlsx)
    TYPE_DOC_TXT = "text"            # Fichiers texte brut (.txt)
    TYPE_DOC_CSV = "csv"            # Fichiers CSV

        # When defining ENUM in PostgreSQL, provide a name for the ENUM type
    # def __init__(self):
    #     super().__init__(name="DocumentTypeEnum")