
from src.dev.models.documentation_model import Documentation
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.bot_model import Bot  #, RAGSettings
from sqlalchemy.orm import Session
from .llama_service import handle_llama_model
from .gpt_service import handle_gpt_model
from .mistral_service import handle_mistral_model
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation


def get_response(bot_id: int, question: str, db: Session):
    bot = db.query(Bot).filter(Bot.idBot == bot_id).first()
   # rag_settings = db.query(RAGSettings).filter(RAGSettings.id == bot.rag_settings_id).first()
    rag_settings = {
     '''   "num_keep": 5,
        "seed": 42,
        "num_predict": 100,
        "top_k": 20,
        "top_p": 0.9,
        "tfs_z": 0.5,
        "typical_p": 0.7,
        "repeat_last_n": 33,
        "temperature": 0.8,
        "repeat_penalty": 1.2,
        "presence_penalty": 1.5,
        "frequency_penalty": 1.0,
        "mirostat": 1,
        "mirostat_tau": 0.8,
        "mirostat_eta": 0.6,
        "penalize_newline": true,
        "stop": ["\n", "user:"],
        "numa": false,
        "num_ctx": 1024,
        "num_batch": 2,
        "num_gqa": 1,
        "num_gpu": 1,
        "main_gpu": 0,
        "low_vram": false,
        "f16_kv": true,
        "vocab_only": false,
        "use_mmap": true,
        "use_mlock": false,
        "rope_frequency_base": 1.1,
        "rope_frequency_scale": 0.8,
        "num_thread": 8'''
    }

    model_switch = {
        'LLAMA': handle_llama_model,
        'GPT': handle_gpt_model,
        'MISTRAL': handle_mistral_model,
        # Add more mappings for different models as needed
    }

    # ai_model defined by default in bot as LLAMA chang eit to test other models 
    handler = model_switch.get(bot.ai_model)

    if handler is None:
        raise ValueError(f"Unsupported AI model: {bot.ai_model}")

    response = handler(question, rag_settings)
    # Add more conditions for different models as needed

    return response

 

def get_document_url_by_id(doc_id: int, db: Session):
    document = db.query(Documentation).filter(Documentation.idDocumentation == doc_id).first()
    return document.url if document else None

