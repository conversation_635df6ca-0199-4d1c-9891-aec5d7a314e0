#!/bin/bash

set -e

# Nom du token Kibana à générer
SERVICE_TOKEN_NAME="kibana-service-token"
NAMESPACE="elastic/kibana"

echo "🧹 Arrêt et suppression des anciens conteneurs (si existants)..."
docker compose down -v

echo "🚀 Démarrage d'Elasticsearch seul pour initialisation..."
docker compose up -d elasticsearch

echo "⏳ Attente de la disponibilité d'Elasticsearch (30s)..."
sleep 30

echo "🔐 Génération du token de service Kibana..."
TOKEN=$(docker exec -i elasticsearch bin/elasticsearch-service-tokens create $NAMESPACE $SERVICE_TOKEN_NAME | grep -oE "AAEAA[A-Za-z0-9_-]+")

if [ -z "$TOKEN" ]; then
  echo "❌ Échec lors de la création du token. Vérifiez les logs Elasticsearch."
  exit 1
fi

echo "✅ Token généré avec succès :"
echo "--------------------------------------------------"
echo "$TOKEN"
echo "--------------------------------------------------"

echo "📦 Mise à jour du token dans le docker-compose.yml..."
# Sauvegarde de l’ancien fichier
cp docker-compose.yml docker-compose.yml.bak

# Mise à jour du token dans le fichier docker-compose.yml
sed -i.bak "s|ELASTICSEARCH_SERVICEACCOUNTTOKEN=.*|ELASTICSEARCH_SERVICEACCOUNTTOKEN=$TOKEN|" docker-compose.yml

echo "🚀 Démarrage de Kibana..."
docker compose up -d kibana

echo ""
echo "🎉 Configuration terminée !"
echo "🔗 Accès Kibana : http://localhost:16407"
echo "🔗 Test API Elasticsearch : curl -u elastic:admin12024 http://localhost:16405/_cat/indices?v"
echo ""
echo ""
echo ""