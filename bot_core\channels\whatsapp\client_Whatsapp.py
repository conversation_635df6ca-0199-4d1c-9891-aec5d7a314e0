import requests
from src.dev.config import get_settings
# il faut génerer un token : Meta > WhatsApp > Configuration API
# Pour url : Meta > WhatsApp > Configuration API d'après curl
settings = get_settings()
def send_message(to_number: str, message: str):
    payload = {
        "messaging_product": "whatsapp",
        "to": to_number,
        "type": "text",
        "text": {"body": message}
    }

    headers = {
        "Authorization": f"Bearer {settings.WHATSAPP_TOKEN}",
        "Content-Type": "application/json"
    }

    response = requests.post(settings.WHATSAPP_API_URL, json=payload, headers=headers)
    if response.status_code >= 400:
        print(f"Error sending WhatsApp message: {response.text}")
    else:
        print(f"✅ Message envoyé à {to_number}")
