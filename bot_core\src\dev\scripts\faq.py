
from sqlalchemy.orm import Session
from src.dev.scripts.database import INFO_LOG, WAR_LOG
from src.dev.config.config import Settings, get_settings
from src.dev.utils.database import SessionLocal
from src.dev.utils.dependencies import get_db

from src.dev.models.question_response_model import FeedbackStatus, QuestionResponseLog
from src.dev.models.user_model import User
from src.dev.models.bot_model import Bo<PERSON>
from src.dev.models.BotIncludesDocumentation import BotIncludeDocumentation
from src.dev.models.subscription_model import Subscription
from src.dev.models.payment_model import Payment
from src.dev.models.pack_model import Pack
from src.dev.models.documentation_model import Documentation

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import re
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from collections import Counter
from unidecode import unidecode
from collections import defaultdict
from elasticsearch import Elasticsearch
from datetime import datetime


# Download necessary NLTK data
nltk.download('stopwords')
nltk.download('punkt')


INDEX="faq"


def get_new_questions(session: Session, last_run_date):
    query = session.query(QuestionResponseLog).filter(
        QuestionResponseLog.feedback.in_(['1', '2', '3'])
    )
    if last_run_date:
        query = query.filter(QuestionResponseLog.timeQuestion > last_run_date)
    return query.all()



def preprocess_text(text):
    text = unidecode(text.lower())
    tokens = word_tokenize(text)
    stop_words = set(stopwords.words('french'))
    tokens = [token for token in tokens if token.isalnum() and token not in stop_words]
    return ' '.join(tokens)
    
def cluster_questions(questions, threshold=0.6):
    vectorizer = TfidfVectorizer(ngram_range=(1, 2))
    tfidf_matrix = vectorizer.fit_transform(questions)
    similarity_matrix = cosine_similarity(tfidf_matrix)
    
    clusters = []
    processed = set()
    
    for i in range(len(questions)):
        if i not in processed:
            cluster = [i]
            processed.add(i)
            for j in range(i+1, len(questions)):
                if j not in processed and similarity_matrix[i][j] > threshold:
                    cluster.append(j)
                    processed.add(j)
            clusters.append(cluster)
    
    return clusters

def find_clusters(session: Session, qals):
  bot_questions = defaultdict(list)
  for qal in qals:
    bot_questions[qal.idBot].append(qal)
  
  all_results = []
  for bot_id, bot_qals in bot_questions.items():
    preprocessed_questions = [preprocess_text(qal.question) for qal in bot_qals]
    clusters = cluster_questions(preprocessed_questions)
    largest_clusters = sorted(clusters, key=len, reverse=True)[:10]
    
    print(f'Bot id: {bot_id}\n')
    bot_results = []
    for cluster in largest_clusters:
      representative = bot_qals[cluster[0]]
      cluster_size = len(cluster)
      
      print(f'Cluster size: {cluster_size}')
      for item in cluster:
        print(f'Question: {bot_qals[item].question}')
      print('\n')
      
      bot_results.append({
          "cluster_size": cluster_size,
          "representative": representative,
          "questions": [bot_qals[item].question for item in cluster]
    })
    
    print('\n--------------------------------------------\n')
    all_results.append({
        "bot_id": bot_id,
        "clusters": bot_results
    })
  
  return all_results


def create_faq_index(es):
  try:
    mappings_string = {
          "properties": {
            "idBot": {"type": "integer"},
            "idLog": {"type": "integer"},
            "question": {"type": "keyword"},
            "response": {"type": "keyword"},
            "langue": {"type": "keyword"},
            "responseSource": {"type": "keyword"},
            "feedback": {"type": "keyword"},
            "frequency": {"type": "integer"}
        }
    }
    es.indices.create(index=INDEX, mappings=mappings_string)

  except:
    print(f'error while creating the index {INDEX}') 

def is_similar(question1, question2, threshold=0.6):
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform([question1, question2])
    similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
    return similarity > threshold

def main() -> None:
  session: Session = SessionLocal()
  print('\n\n')

  settings:Settings = get_settings()

  es_client = Elasticsearch(
            settings.ELASTIC_SERVER,
            #basic_auth=(settings.ELASTIC_USER, settings.ELASTIC_PASSWORD),
            verify_certs=False
  )

  if not es_client.indices.exists(index=INDEX):
    create_faq_index(es_client)
  if not es_client.indices.exists(index='faq-metadata'):
    es_client.indices.create(index='faq-metadata')
  

  try:
    last_run_doc = es_client.get(index='faq-metadata', id="last_run_date")
    last_run_date = last_run_doc['_source']['date']
  except:
    last_run_date = None


  questions = get_new_questions(session, last_run_date)
  clusters = find_clusters(session, questions)
  

  for cluster in clusters:
    bot_id = cluster["bot_id"]
    existing_faqs = es_client.search(index='faq', query={"term": {"idBot": bot_id}}, size=10_000)
    existing_faqs = {hit['_source']['question']: hit for hit in existing_faqs['hits']['hits']}

    for res in cluster["clusters"]:
      similar_faq = None
      for existing_question, existing_hit in existing_faqs.items():
        if is_similar(res["representative"].question, existing_question):
          similar_faq = existing_hit
          break
      
      if similar_faq:
        updated_doc = similar_faq['_source']
        print(f'id: {similar_faq["_id"]}')
        updated_doc['frequency'] += res["cluster_size"]
        es_client.update(index="faq", id=similar_faq['_id'], doc=updated_doc)
        print(f"Updated existing FAQ: {updated_doc['question']}")
      else:
        schema_kwargs = {
            "idBot": cluster["bot_id"],
            "question": res["representative"].question,
            "response": res["representative"].response,
            "responseSource": res["representative"].responseSource,
            "langue": res["representative"].langue,
            "feedback": res["representative"].feedback,
            "idLog": res["representative"].idLog,
            "frequency": res["cluster_size"]
        }
        es_client.index(index="faq", document=schema_kwargs, id=str(res["representative"].idLog))
        print(f"Added new FAQ: {schema_kwargs['question']}")

  last_run_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
  es_client.index(index='faq-metadata', id="last_run_date", document={"date": last_run_date})
  
  print(f'last time run : {last_run_date}')
  print(f'{len(questions)} questions trouve.\n')

  session.close()


if __name__ == '__main__':
  try:
    main()
  except Exception as e:
    raise e
