# ─────────────────────────────────────────────────────────────
# Python standard library
from datetime import datetime, timedelta
import json
import logging
import uuid
from typing import List, Optional

# ─────────────────────────────────────────────────────────────
# Third-party libraries
import requests
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import Select, update, desc
from sqlalchemy.orm import Session, joinedload
from langchain.prompts import PromptTemplate

# ─────────────────────────────────────────────────────────────
# Project configuration
from src.dev.utils.ask_llm_utils import core_ask_logic
from src.dev.config.config import Settings, get_settings
from src.dev.services.rag.rag_processor_factory import get_rag_processor

# ─────────────────────────────────────────────────────────────
# Database models & enums
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.bot_model import Bot
from src.dev.models.documentation_model import Documentation
from src.dev.models.enums.user_type import UserType
from src.dev.models.question_response_model import (
    FeedbackStatus,
    QuestionResponseLog,
    PriorityEnum,
    ResponseSourceEnum,
)

# ─────────────────────────────────────────────────────────────
# Schemas (Pydantic)
from src.dev.schemas.auth import UserSchema
from src.dev.schemas.faq_schema import FAQCreate, FAQResponse, FAQUpdate

# ─────────────────────────────────────────────────────────────
# Services
from src.dev.services.ai.ai_service import get_response, get_document_url_by_id
from src.dev.services.ai.ai_selector import select_model
from src.dev.services.auth.auth import api_key_security, get_current_user_or_visitor
from src.dev.services.bots.bots import BotServices

# ─────────────────────────────────────────────────────────────
# Utilities & helpers
from src.dev.utils.auth import RoleChecker
from src.dev.utils.dependencies import get_db
from src.dev.utils.text_extraction import get_token_count
from src.dev.utils.token_utils import compute_total_tokens, detect_language, update_token_counters
from src.dev.utils.prompt_utils import get_secure_prompt_template, get_multilingual_prompt_params
from src.dev.utils.elasticsearch_utils import safe_es_vector_search



router = APIRouter()

# Fonction utilitaire pour gérer les enums
def safe_enum_value(enum_field, default_value):
    """
    Fonction utilitaire pour extraire la valeur d'un enum de manière sécurisée
    """
    if enum_field is None:
        return default_value
    if hasattr(enum_field, 'value'):
        return enum_field.value
    return enum_field





# Conversation Management Functions
def get_or_create_conversation_id(user_id: int, bot_id: int, db: Optional[Session]) -> str:
    """
    Récupère la conversation active la plus récente ou en crée une nouvelle
    Une conversation est considérée comme active si le dernier message date de moins de 30 minutes
    """
    thirty_minutes_ago = (datetime.utcnow() - timedelta(minutes=30))
    
    if db is None:
        raise ValueError("La session de base de données (db) est requise")

    # Chercher la dernière conversation active
    last_message = (
        db.query(QuestionResponseLog)
        .filter(
            QuestionResponseLog.userId == user_id,
            QuestionResponseLog.idBot == bot_id,
            QuestionResponseLog.timeQuestion >= thirty_minutes_ago,
            QuestionResponseLog.conversationId.isnot(None)
        )
        .order_by(desc(QuestionResponseLog.timeQuestion))
        .first()
    )
    
    if last_message is not None and last_message.conversationId is not None:
        return str(last_message.conversationId)
    else:
        # Créer une nouvelle conversation
        return str(uuid.uuid4())

def create_question_response_log(
    bot_id: int,
    user_id: int, 
    question: str,
    response: str,
    conversation_id: Optional[str] = None,
    response_source: ResponseSourceEnum = ResponseSourceEnum.ia,
    priority: PriorityEnum = PriorityEnum.medium,
    agent_user_id: Optional[int] = None,
    db: Optional[Session] = None
) -> QuestionResponseLog:
    """
    Crée une nouvelle entrée de log avec gestion automatique de conversation
    """
    if conversation_id is None:
        conversation_id = get_or_create_conversation_id(user_id, bot_id, db)
    
    log_entry = QuestionResponseLog(
        id_bot=bot_id,
        user_id=user_id,
        question=question,
        response=response,
        conversation_id=conversation_id,
        response_source=response_source,
        priority=priority,
        escalated=False,
        escalated_reason=None,
        agent_user_id=agent_user_id,
        timeQuestion=datetime.utcnow()
    )
    
    return log_entry

# # Main Ask Question Endpoint
# @router.post("/api/ask/{bot_id}")
# def ask_question(bot_id: int, question: str,
#                  db: Session = Depends(get_db),
#                  api_key: str = Depends(api_key_security),
#                  user = Depends(get_current_user_or_visitor)):
#     """
#     Basic question-answering endpoint with authentication and token tracking
#     """
#     logger = logging.getLogger("uvicorn")
#     logger.info(f"📥 Basic ask request for bot {bot_id}: '{question[:50]}...'")

#     response_source = ResponseSourceEnum.ia
    
#     es_results = None

    
#     try:
#         settings: Settings = get_settings()

#         # === PROTECTION: encapsule même les appels vers safe_es_vector_search
#         try:
#             es_results, vector_value = safe_es_vector_search(question, bot_id)
#         except Exception as es_ex:
#             logger.warning(f"Elasticsearch crash: {es_ex}")
#             es_results, vector_value = None, None


#         # Determine response
#         if not es_results or (es_results['hits']['max_score'] or 0) < 0.95:
#             response = get_response(bot_id, question, db)
#             response_source = ResponseSourceEnum.ia
#         else:
#             response = es_results['hits']['hits'][0]['_source']['response']
#             response_source = ResponseSourceEnum.elastic

#         # Create log entry with conversation management
#         log_entry = create_question_response_log(
#             bot_id=bot_id,
#             user_id=user.user_id,
#             question=question,
#             response=response,
#             response_source=response_source,
#             priority=PriorityEnum.medium,
#             db=db
#         )

#         db.add(log_entry)
#         db.commit()

#         # Update token counters for non-admin users
#         update_token_counters(db, user, bot_id, question, response)

#         return {
#             "msg": response,
#             "conversation_id": log_entry.conversationId,
#             "message_id": log_entry.idLog
#         }
#     except Exception as e:
#         logger.exception(f"❌ Error in ask_question: {str(e)}")
#         raise HTTPException(status_code=404, detail=f"Error in ask_question: {str(e)}")


# ask wiam Hajar
# Enhanced AskLLM Endpoint
@router.post("/api/v1/bot/{bot_id}/ask")
async def askLLM(bot_id: int, query: str,
                 show_sources: bool = Query(default=True, description="Whether to include sources in the response"),
                 db: Session = Depends(get_db),
                 settings: Settings = Depends(get_settings),
                 api_key: str = Depends(api_key_security),
                 user: UserSchema = Depends(get_current_user_or_visitor)):
    """
    askLLM : Enhanced LLM endpoint with structured response format
    """

    logger = logging.getLogger("uvicorn")
    try:
        result = await core_ask_logic(query=query, bot_id=bot_id, db=db, settings=settings)
        response = result["msg"]
        context_passages = result["context"]
        sources = result["sources"]
        response_source = result["response_source"]

        log_entry = None
        try:
            logger.info(f"👉 Type de responseSource : {type(response_source)} - Value: {response_source}")

            log_entry = create_question_response_log(
                bot_id=bot_id,
                user_id=user.user_id,
                question=query,
                response=response,
                response_source=response_source,
                priority=PriorityEnum.medium,
                db=db
            )
            db.add(log_entry)
            db.commit()
        except Exception as e:
            logger.exception(f"⚠️ Error during saving log entry: {str(e)}")

        # Update token counters for non-admin users
        try:
            update_token_counters(db, user, bot_id, query, response)
        except Exception as e:
            logger.exception(f"⚠️ Error updating token counters: {str(e)}")

        # Prepare response based on show_sources parameter
        response_data = {
            "msg": response,
            "context": context_passages,
            "conversation_id": log_entry.conversationId if log_entry else None,
            "message_id": log_entry.idLog if log_entry else None,
        }

        # Only include sources if show_sources is True
        if show_sources:
            response_data["sources"] = sources

        return response_data

    except Exception as e:
        logger.exception(f"❌ Error processing request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in askLLM: {str(e)}")


@router.post("/api/v1/bot/{bot_id}/askWithReranking")
async def askLLMWithReranking(
        bot_id: int,
        query: str,
        db: Session = Depends(get_db),
        settings: Settings = Depends(get_settings),
        api_key: str = Depends(api_key_security),
        user: UserSchema = Depends(get_current_user_or_visitor),
):
    """
    Enhanced endpoint that implements reranking for more accurate context retrieval
    """
    logger = logging.getLogger("uvicorn")
    logger.info(f"📥 Reranking request received for bot {bot_id}: '{query[:50]}...'")

    # Detect language and get appropriate parameters
    lang = detect_language(query)
    prompt_params = get_multilingual_prompt_params(lang)
    qa_prompt_tmpl_str = get_secure_prompt_template()
    response_source = ResponseSourceEnum.ia

    # Get RAG processor
    processor = get_rag_processor()
    
    es_results, vector_value = safe_es_vector_search(query, bot_id)

    try:
        if not es_results or (es_results['hits']['max_score'] or 0) < 0.95:
            # No close match in FAQs, use enhanced RAG with reranking

            # Load chunks and tokenized corpus for reranking
            data = processor.load_chunks_with_tokens(bot_id, db)
            chunks = data["chunks"]
            tokenized_corpus = data["tokenized_corpus"]

            # Hybrid retrieval combines semantic and keyword search
            retrieved_passages = processor.hybrid_retrieval(query, chunks, tokenized_corpus, top_k=7)

            # Reranking improves relevance of retrieved passages
            relevant_context = processor.rerank_passages(query, retrieved_passages)
            logger.info(f"✅ Selected {len(relevant_context)} passages after reranking")

            context = "\n\n".join(relevant_context)
            docs = relevant_context

            # Calculate total tokens to select appropriate model
            total_tokens = compute_total_tokens(context + query, lang)
            logger.info(f"🔢 Estimated total tokens: {total_tokens}")
            model_name = select_model(total_tokens)
            logger.info(f"🤖 Selected model: {model_name}")

            # Format prompt with context and query using security template
            fmt_prompt = qa_prompt_tmpl_str.format(
                CONTEXT=context,
                QUERY=query,
                **prompt_params
            )

            # Send request to Ollama LLM server
            ollama_data = {
                "model": model_name,
                "prompt": fmt_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.0,
                    "top_p": 0.8
                }
            }

            res = requests.post(settings.OLLAMA_SERVER, json=ollama_data)
            logger.info(f"✅ Ollama response status: {res.status_code}")

            if res.status_code == 200:
                response_text = res.text.strip()

                # Verify response before parsing JSON
                if not response_text:
                    raise HTTPException(status_code=500, detail="Empty response from Ollama")

                try:
                    response_json = json.loads(response_text)
                    response = response_json.get("response", "").strip()
                    if not response:
                        raise HTTPException(status_code=500, detail="Empty response field in Ollama response")
                except json.JSONDecodeError as je:
                    logger.error(f"❌ JSON parsing failure: {je}")
                    raise HTTPException(status_code=500, detail="JSON parsing error in Ollama response")
            else:
                raise HTTPException(status_code=res.status_code, detail=f"Ollama server error: {res.text}")
        else:
            # Use cached response from FAQ database
            response = es_results['hits']['hits'][0]['_source'].get('response', '')
            docs = ['Elastic Search FAQ Match']
            response_source = ResponseSourceEnum.elastic

        # Create log entry with conversation management
        log_entry = create_question_response_log(
            bot_id=bot_id,
            user_id=user.user_id,
            question=query,
            response=response,
            response_source=response_source,
            priority=PriorityEnum.medium,
            db=db
        )

        db.add(log_entry)
        db.commit()

        # Update token counters for non-admin users
        update_token_counters(db, user, bot_id, query, response)

        return {
            "msg": response,
            "context": docs,
            "conversation_id": log_entry.conversationId,
            "message_id": log_entry.idLog
        }

    except Exception as e:
        logger.exception("⚠️ Error during reranking processing")
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")


@router.post("/api/v1/bot/{bot_id}/askWhatsapp")
async def askLLM2(bot_id: int, query: str,
                  db: Session = Depends(get_db),
                  settings: Settings = Depends(get_settings)):

    logger = logging.getLogger("uvicorn")
    try:
        result = await core_ask_logic(query=query, bot_id=bot_id, db=db, settings=settings)
        return {"msg": result["msg"], "context": result["context"]}
    except Exception as e:
        logger.exception(f"❌ Error processing test request: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Error in askLLM2: {str(e)}")



# Get Conversations (Grouped by conversation_id)
@router.get("/api/bots/{bot_id}/conversations")
async def get_conversations(
    bot_id: int, 
    user: UserSchema = Depends(get_current_user_or_visitor), 
    session: Session = Depends(get_db),
    _: str = Depends(api_key_security)
):
    """
    Récupère toutes les conversations de l'utilisateur avec le bot, groupées par conversation_id
    """
    stmt = (
        Select(QuestionResponseLog)
        .where(
            QuestionResponseLog.idBot == bot_id, 
            QuestionResponseLog.userId == user.user_id,
            QuestionResponseLog.conversationId.isnot(None)
        )
        .options(joinedload(QuestionResponseLog.user), joinedload(QuestionResponseLog.bot))
        .order_by(QuestionResponseLog.conversationId, QuestionResponseLog.timeQuestion)
    )
    messages = list(session.scalars(stmt).all())
    
    if not messages:
        return []  # Return empty array instead of 404
    
    # Grouper les messages par conversation
    conversations = {}
    for message in messages:
        conv_id = message.conversationId
        if conv_id not in conversations:
            conversations[conv_id] = {
                "conversation_id": conv_id,
                "messages": [],
                "created_at": message.timeQuestion,
                "last_message_at": message.timeQuestion,
                "message_count": 0
            }
        
        conversations[conv_id]["messages"].append({
            "id": message.idLog,
            "question": message.question,
            "response": message.response,
            "timestamp": message.timeQuestion,
            "feedback": message.feedback,
            "escalated": message.escalated,
            # FIX: Utilisation de la fonction utilitaire
            "response_source": safe_enum_value(message.responseSource, "ia"),
            # FIX: Utilisation de la fonction utilitaire
            "priority": safe_enum_value(message.priority, "medium")
        })
        
        conversations[conv_id]["message_count"] += 1
        
        # Mettre à jour le timestamp du dernier message
        if message.timeQuestion > conversations[conv_id]["last_message_at"]:
            conversations[conv_id]["last_message_at"] = message.timeQuestion
    
    # Convertir en liste et trier par dernière activité
    conversation_list = list(conversations.values())
    conversation_list.sort(key=lambda x: x["last_message_at"], reverse=True)
    
    # FIX: ADD THE MISSING RETURN STATEMENT
    return conversation_list

# Get Specific Conversation Messages
@router.get("/api/bots/{bot_id}/conversations/{conversation_id}")
async def get_conversation_messages(
        bot_id: int,
        conversation_id: str,
        user: UserSchema = Depends(get_current_user_or_visitor),
        session: Session = Depends(get_db),
        _: str = Depends(api_key_security)
):
    """
    Récupère tous les messages d'une conversation spécifique
    """
    stmt = (
        Select(QuestionResponseLog)
        .where(QuestionResponseLog.idBot == bot_id)
        .where(QuestionResponseLog.userId == user.user_id)
        .where(QuestionResponseLog.conversationId == conversation_id)
        .order_by(QuestionResponseLog.timeQuestion)
    )
    messages = list(session.scalars(stmt).all())
    
    if not messages:
        raise HTTPException(status_code=404, detail="Conversation non trouvée")
    
    return {
        "conversation_id": conversation_id,
        "messages": [
            {
                "id": msg.idLog,
                "question": msg.question,
                "response": msg.response,
                "timestamp": msg.timeQuestion,
                "feedback": msg.feedback,
                "escalated": msg.escalated,
                # FIX: Correction pour responseSource
                "response_source": (
                    msg.responseSource.value 
                    if hasattr(msg.responseSource, 'value') and msg.responseSource is not None
                    else msg.responseSource or "ia"
                ),
                # FIX: Correction pour priority
                "priority": (
                    msg.priority.value 
                    if hasattr(msg.priority, 'value') and msg.priority is not None
                    else msg.priority or "medium"
                )
            }
            for msg in messages
        ]
    }

# Get All Messages (Legacy endpoint - kept for backward compatibility)
@router.get("/api/bots/{bot_id}/messages")
async def get_messages(
    bot_id: int, 
    user: UserSchema = Depends(get_current_user_or_visitor), 
    session: Session = Depends(get_db),
    _: str = Depends(api_key_security)
):
    """
    Retrieve conversation history for a specific bot and user (updated with conversation support)
    """
    stmt = (
        Select(QuestionResponseLog)
        .where(QuestionResponseLog.idBot == bot_id, QuestionResponseLog.userId == user.user_id)
        .options(joinedload(QuestionResponseLog.user), joinedload(QuestionResponseLog.bot))
        .order_by(desc(QuestionResponseLog.timeQuestion))
    )
    messages = list(session.scalars(stmt).all())
    
    if not messages:
        return []
    
    return [
        {
            "id": msg.idLog,
            "question": msg.question,
            "response": msg.response,
            "timestamp": msg.timeQuestion,
            "feedback": msg.feedback,
            "escalated": msg.escalated,
            "conversation_id": msg.conversationId,
            # FIX: Correction pour responseSource
            "response_source": (
                msg.responseSource.value 
                if hasattr(msg.responseSource, 'value') and msg.responseSource is not None
                else msg.responseSource or "ia"
            ),
            # FIX: Correction pour priority  
            "priority": (
                msg.priority.value 
                if hasattr(msg.priority, 'value') and msg.priority is not None
                else msg.priority or "medium"
            )
        }
        for msg in messages
    ]

# Utility endpoint to end a conversation manually
@router.post("/api/bots/{bot_id}/conversations/{conversation_id}/end")
async def end_conversation(
    bot_id: int,
    conversation_id: str,
    user: UserSchema = Depends(get_current_user_or_visitor),
    session: Session = Depends(get_db),
    _: str = Depends(api_key_security)
):
    """
    Termine manuellement une conversation (optionnel)
    Cette fonctionnalité peut être utilisée pour forcer la fin d'une conversation
    """
    # Vérifier que la conversation appartient à l'utilisateur
    stmt = (
        Select(QuestionResponseLog)
        .where(
            QuestionResponseLog.idBot == bot_id,
            QuestionResponseLog.userId == user.user_id,
            QuestionResponseLog.conversationId == conversation_id
        )
        .limit(1)
    )
    
    message = session.scalars(stmt).first()
    if not message:
        raise HTTPException(status_code=404, detail="Conversation non trouvée")
    
    # Pour forcer une nouvelle conversation, on peut ajouter un flag ou simplement
    # laisser le système créer une nouvelle conversation au prochain message
    return {"message": "Conversation terminée", "conversation_id": conversation_id}

# Other existing endpoints remain the same...
@router.get("/documentation/{doc_id}")
def get_documentation(
    doc_id: int, 
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))):
    """
    Get URL for a specific documentation file
    """
    url = get_document_url_by_id(doc_id, db)
    if not url:
        raise HTTPException(status_code=404, detail="Documentation not found")
    return {"url": url}

# @router.post("/vectorize/{doc_id}")
# def vectorize_document(
#     doc_id: int, 
#     db: Session = Depends(get_db),
#     user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))):
#     """
#     Process and vectorize a document for RAG retrieval
#     """
#     url = get_document_url_by_id(doc_id, db)

#     # Get RAG processor
#     processor = get_rag_processor()

#     if not url:
#         raise HTTPException(status_code=404, detail="Documentation not found")
    
#     processor.vectorize_document_and_store_in_database(url, 111111111, doc_id, db)
#     return {"message": "Document vectorized and stored successfully"}

@router.get("/query")
def query_document(
    prompt: str, 
    document_id: int, 
    db: Session = Depends(get_db),
    user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))):
    """
    Direct RAG query against a specific document
    """
    try:
        document_url = get_document_url_by_id(document_id, db)
        if not document_url:
            raise HTTPException(status_code=404, detail="Document not found.")
        
        response = process_rag_query(prompt, document_url, db)
        return {"response": response}
    except HTTPException as http_err:
        raise http_err
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in query_document: {str(e)}")

@router.get('/api/v1/bots/{bot_id}/config-file')
async def generate_bot_config(
    bot_id: int,
    user: UserSchema = Depends(RoleChecker([UserType.admin, UserType.client])),
    db: Session = Depends(get_db)
):
    return BotServices(db).get_bot_conf_file(user, bot_id)


@router.get('/bots/{bot_id}/faqs')
async def get_bot_faq(bot_id: int,
                      db: Session = Depends(get_db),
                      user = Depends(get_current_user_or_visitor)
                      ):
    return BotServices(db).bot_faq(bot_id)

@router.get("/bots/{bot_id}/faqss", response_model=List[FAQResponse])
def list_faq(
        bot_id: int,
        db: Session = Depends(get_db),
        user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client])),
        langue: Optional[str] = Query(None),
        status: Optional[FeedbackStatus] = Query(None),
        date_debut: Optional[datetime] = Query(None),
        date_fin: Optional[datetime] = Query(None)
):
    query = db.query(QuestionResponseLog).filter(
        QuestionResponseLog.feedback.in_([
            FeedbackStatus.INSATISFAISANTE,
            FeedbackStatus.REVISEE,
            FeedbackStatus.TRAITEE
        ]),
        QuestionResponseLog.idBot == bot_id
    )

    if langue:
        query = query.filter(QuestionResponseLog.langue == langue)
    if status:
        query = query.filter(QuestionResponseLog.feedback == status.value if isinstance(status, Enum) else status)
    if date_debut:
        query = query.filter(QuestionResponseLog.timeQuestion >= date_debut)
    if date_fin:
        query = query.filter(QuestionResponseLog.timeQuestion <= date_fin)

    return query.all()

@router.post("/bots/{bot_id}/faqs", response_model=FAQResponse)
def create_faq(
        faq: FAQCreate,
        db: Session = Depends(get_db),
        user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    new_faq = QuestionResponseLog(**faq.dict())

    db.add(new_faq)
    db.commit()
    db.refresh(new_faq)
    return new_faq

@router.put("/bots/{bot_id}/faqs/{faq_id}", response_model=FAQResponse)
def update_faq(
        bot_id: int,
        faq_id: int,
        faq: FAQUpdate,
        db: Session = Depends(get_db),
        user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    db_faq = db.query(QuestionResponseLog).get(faq_id)

    if not db_faq:
        raise HTTPException(status_code=404, detail="FAQ non trouvée")
    if db_faq.idBot != bot_id:
        raise HTTPException(status_code=400, detail="La FAQ ne correspond pas au bot demandé.")

    for key, value in faq.dict(exclude_unset=True).items():
        setattr(db_faq, key, value)
    db.commit()
    db.refresh(db_faq)
    return db_faq


@router.delete("/bots/{bot_id}/faqs/{faq_id}")
def delete_faq(
        bot_id: int,
        faq_id: int,
        db: Session = Depends(get_db),
        user: UserSchema = Depends(RoleChecker(allowed_roles=[UserType.admin, UserType.client]))
):
    # Récupérer la FAQ par son ID
    db_faq = db.query(QuestionResponseLog).get(faq_id)

    # Vérifier si la FAQ existe
    if not db_faq:
        raise HTTPException(status_code=404, detail="FAQ non trouvée")

    # Vérifier si la FAQ appartient au bot spécifié
    if db_faq.idBot != bot_id:
        raise HTTPException(status_code=400, detail="La FAQ ne correspond pas au bot indiqué.")

    # Logique de suppression basée sur le statut
    if db_faq.feedback == FeedbackStatus.REVISEE.value or db_faq.feedback == FeedbackStatus.INSATISFAISANTE.value:
        # Supprimer directement la FAQ si son statut est "Révisée"
        db.delete(db_faq)
        db.commit()
        return {"message": "FAQ supprimée définitivement."}

    elif db_faq.feedback == FeedbackStatus.TRAITEE.value:
        # Mettre à jour le statut de la FAQ à "Supprimée" si son statut est "Traitée"
        db_faq.feedback = FeedbackStatus.SUPPRIMEE.value
        db.commit()
        return {"message": "FAQ marquée comme supprimée."}

    else:
        raise HTTPException(
            status_code=400,
            detail="Impossible de supprimer cette FAQ avec son statut actuel."
        )

