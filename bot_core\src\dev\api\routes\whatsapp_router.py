import logging
from fastapi import APIRouter, Request, HTTPException,Depends
from channels.whatsapp.webhook import process_whatsapp_message_raw
from src.dev.services.auth.auth import api_key_security, get_current_user_or_visitor
from src.dev.utils.dependencies import get_db
from src.dev.schemas.auth import UserSchema
from sqlalchemy.orm import Session,joinedload
from src.dev.models.question_response_model import QuestionResponseLog
from sqlalchemy import select as Select
from src.dev.models.user_model import User
from src.dev.models.bot_model import Bot
from fastapi.responses import JSONResponse
import asyncio
from fastapi import Request, HTTPException

router = APIRouter()
logger = logging.getLogger("uvicorn")

#Récupération des messages selon le num de télé    "pas utilisé"
@router.get("/api/v1/whatsapp/{phone_number}/messages/")
async def get_messages_by_phone(
    phone_number: str,
    user: UserSchema = Depends(get_current_user_or_visitor),
    session: Session = Depends(get_db),
    _: str = Depends(api_key_security)
):
    # Requête filtrant par  numéro de téléphone 
    stmt = (Select(QuestionResponseLog)
            .join(User, QuestionResponseLog.userId == User.userId)
            .where(User.phone_number == phone_number)
            .options(joinedload(QuestionResponseLog.user))
            .order_by(QuestionResponseLog.timeQuestion)
    )
    messages = list(session.scalars(stmt).all())

    if not messages:
        raise HTTPException(status_code=404, detail="Aucun message trouvé pour ce numéro")

    return messages

#Vérification initiale du token 
# token à mettre au niveau de configuration webhook sur Meta WhatSapp <meta Whatsapp configuration > "https://developers.facebook.com/apps/679004061709990/whatsapp-business/wa-settings/?business_id=1222324352311038"
# il faut activer messages
@router.get("/webhook/whatsapp")
async def verify_token(request: Request):
    params = dict(request.query_params)
    mode = params.get("hub.mode")
    token = params.get("hub.verify_token")
    challenge = params.get("hub.challenge")

    if mode == "subscribe" and token == "docuBot111":
        return int(challenge)
    raise HTTPException(status_code=403, detail="Invalid token")

#Réception des messages venant de WhatsApp Business
@router.post("/webhook/whatsapp")
async def handle_message(request: Request):
    logger.info("📥 Requête POST reçue sur /webhook/whatsapp")
    body = await request.body()
    asyncio.create_task(process_whatsapp_message_raw(body))  # passe en tâche de fond
    return JSONResponse(status_code=200, content={"status": "processing"})



