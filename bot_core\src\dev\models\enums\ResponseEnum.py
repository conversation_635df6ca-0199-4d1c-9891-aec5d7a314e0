from enum import  Enum

class UploadFileResponses(Enum):

    FILE_TYPE_NOT_SUPPORTED = "file_type_not_supported"
    FILE_SIZE_EXCEEDED = "file_size_exceeded"
    FILE_UPLOAD_SUCCESS = "file_upload_success"
    FILE_UPLOAD_FAILED = "file_upload_failed"
            
            # When defining ENUM in PostgreSQL, provide a name for the ENUM type
    # def __init__(self):
    #     super().__init__(name="UploadFileResponses")