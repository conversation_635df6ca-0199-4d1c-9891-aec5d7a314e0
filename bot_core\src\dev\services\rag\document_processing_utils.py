import logging
import re
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

logger = logging.getLogger("uvicorn")

def deduplicate_chunks(chunks, embeddings):
    """
    Supprime les chunks trop similaires en utilisant la similarité cosinus des embeddings.
    """
    unique_chunks = []
    unique_embeddings = []
    
    if not chunks:
        return [], []

    # Convertir les embeddings en tableau numpy pour le calcul de similarité
    # Assurez-vous que les embeddings sont des listes de nombres ou des tableaux numpy
    embeddings_array = np.array(embeddings)

    # Marquer les chunks à conserver
    to_keep = [True] * len(chunks)

    for i in range(len(chunks)):
        if not to_keep[i]:
            continue

        unique_chunks.append(chunks[i])
        unique_embeddings.append(embeddings[i])

        for j in range(i + 1, len(chunks)):
            if not to_keep[j]:
                continue
            
            # Calculer la similarité cosinus entre les embeddings
            # Reshape pour s'assurer que les entrées sont 2D
            similarity = cosine_similarity(embeddings_array[i].reshape(1, -1), embeddings_array[j].reshape(1, -1))[0][0]
            
            if similarity > 0.95:  # Seuil de similarité sémantique
                to_keep[j] = False
                logger.info(f"Deduplicating chunk {j} (page {chunks[j][0]}) due to high semantic similarity with chunk {i} (page {chunks[i][0]})")

    return unique_chunks, unique_embeddings


def verify_page_coverage(chunks, docs_with_pages):
    """
    Vérifie que toutes les pages sont couvertes par au moins un chunk.
    """
    covered_pages = set(page_num for page_num, _ in chunks)
    all_pages = set(page_num for page_num, _ in docs_with_pages)
    missing_pages = all_pages - covered_pages
    
    return {
        "total_pages": len(all_pages),
        "covered_pages": len(covered_pages),
        "missing_pages": list(missing_pages),
        "coverage_percentage": len(covered_pages) / len(all_pages) * 100
    }


