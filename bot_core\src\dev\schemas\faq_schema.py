
from datetime import datetime
from enum import Enum
from typing import Optional
from pydantic import BaseModel, ConfigDict, Field

from src.dev.utils.enums import ResponseSourceEnum

class FaqSchema(BaseModel):
    idBot: int
    question: str
    response: str
    frequency: int 

# Enums
class FeedbackStatus(int, Enum):
    INSATISFAISANTE = 1
    REVISEE = 2
    TRAITEE = 3
    SUPPRIMEE = 4



class PriorityEnum(str, Enum):
    low = "low"
    medium = "medium"
    high = "high"


# Schéma pour la création
class FAQCreate(BaseModel):
    userId: int
    idBot: int
    question: str
    response: str
    langue: str = "fr"
    feedback: FeedbackStatus = FeedbackStatus.REVISEE
    feedback_text: Optional[str] = None
    response_source: ResponseSourceEnum = ResponseSourceEnum.agent
    priority: PriorityEnum = PriorityEnum.medium
    timeQuestion: datetime = Field(default_factory=datetime.utcnow)


# Schéma pour la mise à jour
class FAQUpdate(BaseModel):
    question: Optional[str] = None
    response: Optional[str] = None
    langue: Optional[str] = None
    timeQuestion: datetime = Field(default_factory=datetime.utcnow)


# Schéma pour les réponses API
class FAQResponse(FAQCreate):
    idLog: int
    id_bot: int = Field(..., alias="idBot")      
    user_id: int = Field(..., alias="userId")
    timeQuestion: datetime

    model_config = ConfigDict(from_attributes=True)
