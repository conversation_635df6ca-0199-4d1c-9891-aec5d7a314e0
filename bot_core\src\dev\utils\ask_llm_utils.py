import json
import logging

# ─────────────────────────────────────────────────────────────
# Third-party libraries
import requests
from fastapi import HTTPException
from langchain.prompts import PromptTemplate

# ─────────────────────────────────────────────────────────────
# Project utilities & services
from src.dev.utils.elasticsearch_utils import safe_es_vector_search
from src.dev.utils.prompt_utils import get_secure_prompt_template, get_multilingual_prompt_params
from src.dev.utils.token_utils import compute_total_tokens, detect_language

# ─────────────────────────────────────────────────────────────
# Models & enums
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.bot_model import Bot
from src.dev.models.documentation_model import Documentation
from src.dev.models.question_response_model import ResponseSourceEnum
async def core_ask_logic(query: str, bot_id: int, db, settings):
    
    logger = logging.getLogger("uvicorn")
    lang = detect_language(query)
    logger.info(f"🌐 Detected language: {lang}")
    
    prompt_params = get_multilingual_prompt_params(lang)
    qa_prompt_tmpl_str = get_secure_prompt_template()

    response_source = ResponseSourceEnum.ia
    es_results, vector_value = safe_es_vector_search(query, bot_id)

    def build_document_url(doc_path, base_url="http://localhost"):
        """Construire l'URL du document dans le format souhaité à partir du chemin complet"""
        if doc_path:
            # Extraire les 3 derniers éléments du chemin : id1/id2/filename
            path_parts = doc_path.replace('\\', '/').split('/')
            if len(path_parts) >= 3:
                # Prendre les 3 derniers éléments (id1/id2/filename)
                relevant_path = '/'.join(path_parts[-3:])
                return f"{base_url}/files/docs/{relevant_path}"
            else:
                # Fallback si le chemin n'a pas assez d'éléments
                filename = path_parts[-1] if path_parts else "document.pdf"
                return f"{base_url}/files/docs/{filename}"
        else:
            # Fallback si pas de chemin
            return f"{base_url}/files/docs/document.pdf"

    if not es_results or (es_results['hits']['max_score'] or 0) < 0.95:
        # Database search for similar documents
        bot = db.query(Bot).filter_by(idBot=bot_id).first()
        if not bot:
            raise HTTPException(status_code=404, detail="Bot not found")

        results = (
            db.query(EmbeddingData.chunkText, EmbeddingData.pageNumber, EmbeddingData.docUrl, Documentation)
            .join(Documentation, EmbeddingData.docId == Documentation.idDocumentation)
            .filter(EmbeddingData.botId == bot_id)
            .order_by(EmbeddingData.embeddings.l2_distance(vector_value))
            .limit(5)
            .all()
        )

        # Extract context passages (just the text chunks)
        context_passages = [result[0] for result in results]

        # Build structured sources
        sources = []
        seen_sources = set()  # Pour éviter les doublons

        for result in results:
            chunk_text = result[0]      # Le texte du chunk
            page_number = result[1]     # Le numéro de page
            doc_url = result[2]         # L'URL du document (peut être None)
            doc = result[3]             # L'objet Documentation

            # Titre du document
            document_title = getattr(doc, 'title', f"Document {doc.idDocumentation}")

            # Créer un identifiant unique pour éviter les doublons
            source_id = f"{doc.idDocumentation}_{page_number}_{hash(chunk_text[:50])}"

            if source_id not in seen_sources:
                seen_sources.add(source_id)

                # Prévisualisation du chunk (limité à 150 caractères)
                chunk_preview = chunk_text[:150] + "..." if len(chunk_text) > 150 else chunk_text

                # Construire l'URL personnalisée à partir du chemin complet
                custom_doc_url = build_document_url(doc_url)

                source_info = {
                    "document_title": document_title,
                    "docUrl": custom_doc_url,  # Utiliser l'URL personnalisée
                    "chunk_preview": chunk_preview,
                    "page_number": page_number
                }
                sources.append(source_info)

        # Construire le contexte pour le prompt
        context = "\n\n".join(context_passages)

        logger.info(f"✅ Context extracted from {len(results)} chunks")
        logger.info(f"📚 Number of sources: {len(sources)}")

        # Prepare prompt
        prompt_tmpl = PromptTemplate.from_template(qa_prompt_tmpl_str)
        fmt_prompt = prompt_tmpl.format(
            QUERY=query,
            CONTEXT=context,
            **prompt_params
        )

        # Calculate and optimize tokens if necessary
        total_tokens = compute_total_tokens(fmt_prompt, lang)
        logger.info(f"🔢 Estimated total tokens: {total_tokens}")
        
        if total_tokens > 2000:
            # Shorten context if necessary
            context_lines = context.split('\n\n')
            if len(context_lines) > 3:
                context = '\n\n'.join(context_lines[:3])
                # Aussi limiter les sources correspondantes
                sources = sources[:3]
                # Aussi limiter les passages de contexte
                context_passages = context_passages[:3]
                fmt_prompt = prompt_tmpl.format(
                    QUERY=query,
                    CONTEXT=context,
                    **prompt_params
                )
                logger.info(f"🔧 Optimized prompt tokens: {compute_total_tokens(fmt_prompt, lang)}")

        # Prepare Ollama request
        model_name = "llama3.2" #"llama3.1:8b"
        logger.info(f"🤖 Selected model: {model_name}")

        # 📦 Prépare le payload à envoyer à l'API Ollama (serveur LLM local ou distant)
        # - model : nom du modèle LLM à utiliser
        # - prompt : le texte contenant la question + contexte
        # - stream : false = réponse complète (non streaming)
        # - options : paramètres LLM comme la température (plus bas = plus déterministe)
        # - - temperature : Ajoute du bruit global à toutes les probabilités
        # - - top_p : Filtre les choix les plus probables
        data = {
            "model": model_name,
            "prompt": fmt_prompt,
            "stream": False,
            "options": {
                "temperature": 0.3, # faible température pour une réponse plus précise et stable
                "top_p": 0.95
            }
        }

        # 🚀 Envoie de la requête POST à Ollama Server pour obtenir une réponse LLM
        res = requests.post(settings.OLLAMA_SERVER, json=data, timeout=120)

        logger.info(f"✅ [OLLAMA REQUEST] Status Code: {res.status_code}")

        if res.status_code == 200:
            response = json.loads(res.text)['response']
            response_source = ResponseSourceEnum.ia
        else:
            raise HTTPException(status_code=500, detail=f"Error in Ollama server: {res.text}")

    else:
        # Elasticsearch result
        response = es_results['hits']['hits'][0]['_source']['response']
        es_source = es_results['hits']['hits'][0]['_source']
        es_doc_title = es_source.get('title', 'Elastic Search FAQ Match')
        es_page_number = es_source.get('page_number', None)

        # Pour Elasticsearch, aussi construire l'URL personnalisée
        es_original_path = es_source.get('docUrl', '')
        es_doc_url = build_document_url(es_original_path)

        # Pour Elasticsearch, créer des passages de contexte et sources
        context_passages = [response]  # La réponse elle-même comme contexte

        sources = [{
            "document_title": es_doc_title,
            "docUrl": es_doc_url,
            "chunk_preview": response[:150] + "..." if len(response) > 150 else response,
            "page_number": es_page_number
        }]

        response_source = ResponseSourceEnum.elastic

    logger.info(f"✅ Response generated successfully")
    logger.info(f"📚 Final sources count: {len(sources)}")
    
    return {
        "msg": response,
        "context": context_passages,  # Liste des passages de texte
        "sources": sources,           # Liste structurée des sources
        "response_source": response_source,
    }