import json
import logging
import numpy as np
import os
from datetime import datetime, timed<PERSON><PERSON>
from sklearn.feature_extraction.text import TfidfVectorizer

# ─────────────────────────────────────────────────────────────
# Third-party libraries
import requests
from fastapi import HTTPException
from langchain.prompts import PromptTemplate

# ─────────────────────────────────────────────────────────────
# Project utilities & services
from src.dev.utils.elasticsearch_utils import safe_es_vector_search
from src.dev.utils.prompt_utils import get_secure_prompt_template, get_multilingual_prompt_params
from src.dev.utils.token_utils import compute_total_tokens, detect_language

# ─────────────────────────────────────────────────────────────
# Models & enums
from src.dev.models.Embedding_Data import EmbeddingData
from src.dev.models.bot_model import Bot
from src.dev.models.documentation_model import Documentation
from src.dev.models.question_response_model import ResponseSourceEnum
from src.dev.models.conversation import UserTempData, UserTempChunk
async def core_ask_logic(query: str, bot_id: int, db, settings):
    
    logger = logging.getLogger("uvicorn")
    lang = detect_language(query)
    logger.info(f"🌐 Detected language: {lang}")
    
    prompt_params = get_multilingual_prompt_params(lang)
    qa_prompt_tmpl_str = get_secure_prompt_template()

    response_source = ResponseSourceEnum.ia
    es_results, vector_value = safe_es_vector_search(query, bot_id)

    def build_document_url(doc_path, base_url="http://localhost"):
        """Construire l'URL du document dans le format souhaité à partir du chemin complet"""
        if doc_path:
            # Extraire les 3 derniers éléments du chemin : id1/id2/filename
            path_parts = doc_path.replace('\\', '/').split('/')
            if len(path_parts) >= 3:
                # Prendre les 3 derniers éléments (id1/id2/filename)
                relevant_path = '/'.join(path_parts[-3:])
                return f"{base_url}/files/docs/{relevant_path}"
            else:
                # Fallback si le chemin n'a pas assez d'éléments
                filename = path_parts[-1] if path_parts else "document.pdf"
                return f"{base_url}/files/docs/{filename}"
        else:
            # Fallback si pas de chemin
            return f"{base_url}/files/docs/document.pdf"

    if not es_results or (es_results['hits']['max_score'] or 0) < 0.95:
        # Database search for similar documents
        bot = db.query(Bot).filter_by(idBot=bot_id).first()
        if not bot:
            raise HTTPException(status_code=404, detail="Bot not found")

        results = (
            db.query(EmbeddingData.chunkText, EmbeddingData.pageNumber, EmbeddingData.docUrl, Documentation)
            .join(Documentation, EmbeddingData.docId == Documentation.idDocumentation)
            .filter(EmbeddingData.botId == bot_id)
            .order_by(EmbeddingData.embeddings.l2_distance(vector_value))
            .limit(5)
            .all()
        )

        # Extract context passages (just the text chunks)
        context_passages = [result[0] for result in results]

        # Build structured sources
        sources = []
        seen_sources = set()  # Pour éviter les doublons

        for result in results:
            chunk_text = result[0]      # Le texte du chunk
            page_number = result[1]     # Le numéro de page
            doc_url = result[2]         # L'URL du document (peut être None)
            doc = result[3]             # L'objet Documentation

            # Titre du document
            document_title = getattr(doc, 'title', f"Document {doc.idDocumentation}")

            # Créer un identifiant unique pour éviter les doublons
            source_id = f"{doc.idDocumentation}_{page_number}_{hash(chunk_text[:50])}"

            if source_id not in seen_sources:
                seen_sources.add(source_id)

                # Prévisualisation du chunk (limité à 150 caractères)
                chunk_preview = chunk_text[:150] + "..." if len(chunk_text) > 150 else chunk_text

                # Construire l'URL personnalisée à partir du chemin complet
                custom_doc_url = build_document_url(doc_url)

                source_info = {
                    "document_title": document_title,
                    "docUrl": custom_doc_url,  # Utiliser l'URL personnalisée
                    "chunk_preview": chunk_preview,
                    "page_number": page_number
                }
                sources.append(source_info)

        # Construire le contexte pour le prompt
        context = "\n\n".join(context_passages)

        logger.info(f"✅ Context extracted from {len(results)} chunks")
        logger.info(f"📚 Number of sources: {len(sources)}")

        # Prepare prompt
        prompt_tmpl = PromptTemplate.from_template(qa_prompt_tmpl_str)
        fmt_prompt = prompt_tmpl.format(
            QUERY=query,
            CONTEXT=context,
            **prompt_params
        )

        # Calculate and optimize tokens if necessary
        total_tokens = compute_total_tokens(fmt_prompt, lang)
        logger.info(f"🔢 Estimated total tokens: {total_tokens}")
        
        if total_tokens > 2000:
            # Shorten context if necessary
            context_lines = context.split('\n\n')
            if len(context_lines) > 3:
                context = '\n\n'.join(context_lines[:3])
                # Aussi limiter les sources correspondantes
                sources = sources[:3]
                # Aussi limiter les passages de contexte
                context_passages = context_passages[:3]
                fmt_prompt = prompt_tmpl.format(
                    QUERY=query,
                    CONTEXT=context,
                    **prompt_params
                )
                logger.info(f"🔧 Optimized prompt tokens: {compute_total_tokens(fmt_prompt, lang)}")

        # Prepare Ollama request
        model_name = "llama3.2" #"llama3.1:8b"
        logger.info(f"🤖 Selected model: {model_name}")

        # 📦 Prépare le payload à envoyer à l'API Ollama (serveur LLM local ou distant)
        # - model : nom du modèle LLM à utiliser
        # - prompt : le texte contenant la question + contexte
        # - stream : false = réponse complète (non streaming)
        # - options : paramètres LLM comme la température (plus bas = plus déterministe)
        # - - temperature : Ajoute du bruit global à toutes les probabilités
        # - - top_p : Filtre les choix les plus probables
        data = {
            "model": model_name,
            "prompt": fmt_prompt,
            "stream": False,
            "options": {
                "temperature": 0.3, # faible température pour une réponse plus précise et stable
                "top_p": 0.95
            }
        }

        # 🚀 Envoie de la requête POST à Ollama Server pour obtenir une réponse LLM
        res = requests.post(settings.OLLAMA_SERVER, json=data, timeout=120)

        logger.info(f"✅ [OLLAMA REQUEST] Status Code: {res.status_code}")

        if res.status_code == 200:
            response = json.loads(res.text)['response']
            response_source = ResponseSourceEnum.ia
        else:
            raise HTTPException(status_code=500, detail=f"Error in Ollama server: {res.text}")

    else:
        # Elasticsearch result
        response = es_results['hits']['hits'][0]['_source']['response']
        es_source = es_results['hits']['hits'][0]['_source']
        es_doc_title = es_source.get('title', 'Elastic Search FAQ Match')
        es_page_number = es_source.get('page_number', None)

        # Pour Elasticsearch, aussi construire l'URL personnalisée
        es_original_path = es_source.get('docUrl', '')
        es_doc_url = build_document_url(es_original_path)

        # Pour Elasticsearch, créer des passages de contexte et sources
        context_passages = [response]  # La réponse elle-même comme contexte

        sources = [{
            "document_title": es_doc_title,
            "docUrl": es_doc_url,
            "chunk_preview": response[:150] + "..." if len(response) > 150 else response,
            "page_number": es_page_number
        }]

        response_source = ResponseSourceEnum.elastic

    logger.info(f"✅ Response generated successfully")
    logger.info(f"📚 Final sources count: {len(sources)}")
    
    return {
        "msg": response,
        "context": context_passages,  # Liste des passages de texte
        "sources": sources,           # Liste structurée des sources
        "response_source": response_source,
    }


# ─────────────────────────────────────────────────────────────
# Pipeline RAG Temporaire - Fonctions utilitaires
# ─────────────────────────────────────────────────────────────

def get_or_create_user_temp_data(user_id: str, db):
    """
    Récupère ou crée les données temporaires d'un utilisateur.
    """
    user_data = db.query(UserTempData).filter(UserTempData.user_id == user_id).first()

    if user_data:
        # Vérifier si les données ont expiré
        if datetime.utcnow() > user_data.expires_at:
            # Supprimer les données expirées
            db.delete(user_data)
            db.commit()
            user_data = None

    if not user_data:
        # Créer de nouvelles données utilisateur
        user_data = UserTempData(
            user_id=user_id,
            total_size=0,
            uploaded_files="[]",  # JSON string vide
            last_activity=datetime.utcnow(),
            expires_at=datetime.utcnow() + timedelta(days=2)
        )
        db.add(user_data)
        db.commit()
        db.refresh(user_data)

    return user_data


def update_user_activity(user_id: str, db):
    """
    Met à jour la dernière activité de l'utilisateur.
    """
    user_data = db.query(UserTempData).filter(UserTempData.user_id == user_id).first()
    if user_data:
        user_data.last_activity = datetime.utcnow()
        db.commit()


def get_user_chunks(user_id: str, db):
    """
    Récupère tous les chunks d'un utilisateur.
    """
    user_data = db.query(UserTempData).filter(UserTempData.user_id == user_id).first()
    if not user_data:
        return []

    chunks = db.query(UserTempChunk).filter(
        UserTempChunk.user_data_id == user_data.id
    ).order_by(UserTempChunk.chunk_index).all()

    return [chunk.chunk_text for chunk in chunks]


def save_user_chunks(user_id: str, chunks: list, file_hash: str, db):
    """
    Sauvegarde les chunks d'un utilisateur.
    """
    user_data = get_or_create_user_temp_data(user_id, db)

    # Supprimer les anciens chunks de ce fichier
    existing_chunks = db.query(UserTempChunk).filter(
        UserTempChunk.user_data_id == user_data.id,
        UserTempChunk.file_hash == file_hash
    ).all()

    for chunk in existing_chunks:
        db.delete(chunk)

    # Ajouter les nouveaux chunks
    for i, chunk_text in enumerate(chunks):
        chunk = UserTempChunk(
            user_data_id=user_data.id,
            chunk_text=chunk_text,
            chunk_index=i,
            file_hash=file_hash
        )
        db.add(chunk)

    db.commit()


def keyword_filter_temp(query: str, chunks: list, min_match: int = 1):
    """
    Filtre les chunks basé sur les mots-clés de la requête.
    """
    query_words = set(query.lower().split())
    return [c for c in chunks if sum(w in c.lower() for w in query_words) >= min_match]


def get_top_chunks_temp(query: str, chunks: list, top_k: int = 3, max_chunk_len: int = 400):
    """
    Récupère les meilleurs chunks pour une requête en utilisant TF-IDF.
    """
    if not chunks:
        return []

    # Filtrer par mots-clés d'abord
    filtered = keyword_filter_temp(query, chunks)
    if not filtered:
        filtered = chunks

    # Utiliser TF-IDF pour le scoring
    try:
        vectorizer = TfidfVectorizer().fit_transform([query] + filtered)
        cosine_sim = (vectorizer * vectorizer.T).toarray()
        scores = cosine_sim[0][1:]  # Scores pour chaque chunk

        # Obtenir les indices des meilleurs chunks
        top_indices = np.argsort(scores)[::-1][:top_k]
        selected = [filtered[i][:max_chunk_len] for i in top_indices]

        return selected
    except Exception as e:
        logging.getLogger("uvicorn").error(f"Erreur dans get_top_chunks_temp: {e}")
        return filtered[:top_k]  # Fallback: retourner les premiers chunks


def build_temp_rag_prompt(user_question: str, context: str, history: list = None):
    """
    Construit le prompt pour le pipeline RAG temporaire.
    """
    history_context = ""
    if history:
        recent_history = history[-5:]  # Garder seulement les 5 derniers échanges
        history_context = f"Historique récent: {recent_history}\n"

    return f"""
Tu es un assistant IA. Voici l'historique et le texte du PDF.
{history_context}
Contexte PDF:
{context}

Question: {user_question}
Réponds uniquement avec ce contexte disponible.
Tu peux ajouter des flèches ou icônes pour clarifier les choses à l'utilisateur.
"""


# ─────────────────────────────────────────────────────────────
# Documents temporaires avec LangChain (optionnel)
# ─────────────────────────────────────────────────────────────

def load_temp_documents_context(question: str, filepaths: list, top_k: int = 3):
    """
    Charge les documents temporaires, crée un index vectoriel en mémoire et retourne les passages les plus pertinents.
    Compatible avec Ollama (ex: mistral).

    Args:
        question: La question de l'utilisateur
        filepaths: Liste des chemins vers les fichiers temporaires
        top_k: Nombre de passages à retourner

    Returns:
        Liste des passages de texte les plus pertinents
    """
    try:
        # Import conditionnel pour éviter les erreurs si les dépendances ne sont pas installées
        from langchain.document_loaders import PyMuPDFLoader
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        from langchain.embeddings import OllamaEmbeddings
        from langchain.vectorstores import FAISS

        all_docs = []
        for path in filepaths:
            try:
                if os.path.exists(path) and path.endswith('.pdf'):
                    loader = PyMuPDFLoader(path)
                    all_docs.extend(loader.load())
            except Exception as e:
                logger.error(f"Erreur lors du chargement du document {path}: {e}")
                continue

        if not all_docs:
            logger.warning("Aucun document temporaire valide trouvé")
            return []

        # Diviser en chunks
        splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
        chunks = splitter.split_documents(all_docs)

        if not chunks:
            logger.warning("Aucun chunk généré à partir des documents temporaires")
            return []

        # Créer l'index vectoriel avec Ollama embeddings
        embeddings = OllamaEmbeddings(model="mistral")  # Compatible avec votre environnement
        vectorstore = FAISS.from_documents(chunks, embeddings)

        # Recherche des documents pertinents
        retriever = vectorstore.as_retriever(search_kwargs={"k": top_k})
        results = retriever.get_relevant_documents(question)

        logger.info(f"🧩 {len(results)} passages trouvés dans les documents temporaires")
        return [doc.page_content for doc in results]

    except ImportError as e:
        logger.warning(f"Dépendances LangChain manquantes pour les documents temporaires: {e}")
        return []
    except Exception as e:
        logger.error(f"Erreur dans load_temp_documents_context: {e}")
        return []


def get_temp_documents_for_conversation(conversation_id: str, db):
    """
    Récupère les chemins des documents temporaires non expirés pour une conversation.

    Args:
        conversation_id: ID de la conversation
        db: Session de base de données

    Returns:
        Liste des chemins de fichiers
    """
    try:
        from src.dev.models.conversation import ConversationTempDocument

        temp_docs = db.query(ConversationTempDocument.filepath).filter(
            ConversationTempDocument.conversationId == conversation_id,
            ConversationTempDocument.expiresAt > datetime.utcnow()
        ).all()

        temp_paths = [doc[0] for doc in temp_docs if doc[0] and os.path.exists(doc[0])]
        logger.info(f"📁 {len(temp_paths)} documents temporaires trouvés pour la conversation {conversation_id}")

        return temp_paths

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des documents temporaires: {e}")
        return []


def enrich_context_with_temp_documents(query: str, conversation_id: str, db, existing_context_passages: list = None):
    """
    Enrichit le contexte existant avec les documents temporaires de la conversation.

    Args:
        query: Question de l'utilisateur
        conversation_id: ID de la conversation
        db: Session de base de données
        existing_context_passages: Passages de contexte existants

    Returns:
        Liste combinée des passages de contexte
    """
    if existing_context_passages is None:
        existing_context_passages = []

    # Récupérer les documents temporaires
    temp_paths = get_temp_documents_for_conversation(conversation_id, db)

    if not temp_paths:
        return existing_context_passages

    # Charger le contexte des documents temporaires
    temp_context_passages = load_temp_documents_context(query, temp_paths, top_k=3)

    # Combiner les contextes (documents temporaires en premier pour la priorité)
    combined_context = temp_context_passages + existing_context_passages

    logger.info(f"🔗 Contexte enrichi: {len(temp_context_passages)} passages temporaires + {len(existing_context_passages)} passages existants")

    return combined_context