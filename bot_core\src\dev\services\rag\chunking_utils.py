import re
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from src.dev.services.rag.text_utils import strip_nul, remove_binary_garbage

def get_smart_text_end_improved(content, size):
    """
    Obtient la fin d'un texte en coupant intelligemment (fin de phrase) en utilisant NLTK.
    """
    if len(content) <= size:
        return content
    
    sentences = sent_tokenize(content)
    current_length = 0
    best_cut_content = ""

    # Tenter de couper à la fin d'une phrase
    for sentence in reversed(sentences):
        # Ajouter 1 pour l'espace ou la ponctuation entre les phrases
        if current_length + len(sentence) + 1 <= size + 50: 
            best_cut_content = sentence + " " + best_cut_content
            current_length += len(sentence) + 1
        else:
            break
    
    if best_cut_content.strip():
        return best_cut_content.strip()

    # Si pas de coupure de phrase intelligente, couper au dernier mot complet
    words = word_tokenize(content)
    current_length = 0
    best_cut_words = []
    for word in reversed(words):
        if current_length + len(word) + 1 <= size + 50:
            best_cut_words.insert(0, word)
            current_length += len(word) + 1
        else:
            break
    return " ".join(best_cut_words).strip()


def get_smart_text_start_improved(content, size):
    """
    Obtient le début d'un texte en coupant intelligemment (début de phrase) en utilisant NLTK.
    """
    if len(content) <= size:
        return content
    
    sentences = sent_tokenize(content)
    current_length = 0
    best_cut_content = ""

    # Tenter de couper au début d'une phrase
    for sentence in sentences:
        if current_length + len(sentence) + 1 <= size + 50:
            best_cut_content += sentence + " "
            current_length += len(sentence) + 1
        else:
            break
    
    if best_cut_content.strip():
        return best_cut_content.strip()

    # Si pas de coupure de phrase intelligente, couper au premier mot complet
    words = word_tokenize(content)
    current_length = 0
    best_cut_words = []
    for word in words:
        if current_length + len(word) + 1 <= size + 50:
            best_cut_words.append(word)
            current_length += len(word) + 1
        else:
            break
    return " ".join(best_cut_words).strip()


def find_sentence_continuation(current_content, next_content):
    """
    Détecte si une phrase commence sur une page et se termine sur la suivante.
    """
    # Prendre les 200 derniers caractères de la page courante
    current_end = current_content[-200:].strip()
    # Prendre les 200 premiers caractères de la page suivante  
    next_start = next_content[:200].strip()
    
    # Vérifier si la page courante se termine sans ponctuation finale
    if current_end and not current_end[-1] in ".!?":
        # Chercher si la phrase se termine dans les premiers mots de la page suivante
        words_next = next_start.split()[:20]  # Premiers 20 mots
        
        for i, word in enumerate(words_next):
            if word.endswith((".", "!", "?")):
                # Phrase trouvée !
                continuation = ' '.join(words_next[:i+1])
                complete_sentence = f"{current_end} {continuation}"
                return complete_sentence
    
    return None


def find_concept_bridge(current_content, next_content, overlap_size):
    """
    Trouve des concepts ou termes importants qui apparaissent dans les deux pages.
    """
    # Extraire les mots importants (plus de 4 lettres, pas de mots vides)
    
    stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
    
    current_words = set(re.findall(r'\b[a-zA-Z]{4,}\b', current_content.lower()))
    next_words = set(re.findall(r'\b[a-zA-Z]{4,}\b', next_content.lower()))
    
    # Mots importants communs
    common_words = (current_words & next_words) - stop_words
    
    if len(common_words) >= 2:  # Au moins 2 mots en commun
        # Créer un chunk qui met en relation ces concepts
        current_relevant = extract_sentences_with_words(current_content, common_words, overlap_size//2)
        next_relevant = extract_sentences_with_words(next_content, common_words, overlap_size//2)
        
        if current_relevant and next_relevant:
            return f"{current_relevant}\n[CONCEPT_BRIDGE: {', '.join(list(common_words)[:3])}]\n{next_relevant}"
    
    return None


def extract_sentences_with_words(content, target_words, max_length):
    """
    Extrait les phrases contenant les mots cibles.
    """
    sentences = re.split(r'[.!?]+', content)
    relevant_sentences = []
    
    for sentence in sentences:
        sentence_words = set(re.findall(r'\b[a-zA-Z]{4,}\b', sentence.lower()))
        if sentence_words & target_words:  # Intersection non vide
            relevant_sentences.append(sentence.strip())
    
    # Joindre les phrases pertinentes jusqu'à max_length
    result = ""
    for sentence in relevant_sentences:
        if len(result + sentence) <= max_length:
            result += sentence + ". "
        else:
            break
    
    return result.strip()





