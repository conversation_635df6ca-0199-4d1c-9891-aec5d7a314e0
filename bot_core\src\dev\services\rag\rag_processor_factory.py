import os
import logging
from typing import Optional

from src.dev.services.rag.rag_processor import AbstractRAGProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Factory pour obtenir le processeur RAG approprié
class RAGProcessorFactory:
    @staticmethod
    def get_processor(processor_type: Optional[str] = None) -> AbstractRAGProcessor:
        """
        Factory method to get the appropriate RAG processor
        
        Args:
            processor_type: The type of processor to use ('langchain' or 'llamaindex').
                           If None, it will use the RAG_PROCESSOR_TYPE from environment.
        
        Returns:
            An instance of the selected RAG processor
        """
        if processor_type is None:
            # Utiliser la variable d'environnement RAG_PROCESSOR_TYPE pour déterminer le processeur à utiliser
            processor_type = os.getenv("RAG_PROCESSOR_TYPE", "langchain").lower()
        
        logger.info(f"Using RAG processor type: {processor_type}")
        
        # Using lazy imports to avoid circular dependencies and slow startup
        if processor_type == "langchain":
            # Only import when actually needed
            from src.dev.services.rag.langchain_rag_processor import LangChainRAGProcessor
            return LangChainRAGProcessor()
        elif processor_type == "llamaindex":
            # Only import when actually needed
            from src.dev.services.rag.llamaindex_rag_processor import LlamaIndexRAGProcessor
            return LlamaIndexRAGProcessor()
        else:
            logger.warning(f"Unknown processor type '{processor_type}', falling back to langchain")
            from src.dev.services.rag.langchain_rag_processor import LangChainRAGProcessor
            return LangChainRAGProcessor()

# This variable will hold the processor instance for reuse
_processor_instance = None


    # Function to get the default RAG processor (with caching)
def get_rag_processor() -> AbstractRAGProcessor:
    """
    Get the default RAG processor based on environment settings
    """
    global _processor_instance
    if _processor_instance is None:
        _processor_instance = RAGProcessorFactory.get_processor()
    return _processor_instance