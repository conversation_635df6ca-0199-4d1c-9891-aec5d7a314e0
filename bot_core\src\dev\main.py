import logging
from datetime import datetime
from starlette.middleware.base import BaseHTTPMiddleware
from uvicorn.config import LOGGING_CONFIG
import time
from fastapi import FastAPI, Request
from src.dev.api.routes import auth, visitor_routes,agent_routes

from src.dev.api.routes import  bot_routes, client_routes
from src.dev.api.routes import base, pack_routes, payement_routes
from src.dev.utils.database import Base,engine

from fastapi.middleware.cors import CORSMiddleware
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from src.dev.api.routes.souscription_routes import souscription_routes
from src.dev.api.routes.user_pack_api import user_pack_router
from src.dev.models.dashboard_client_router import dashboard_client
from src.dev.models.dashboard_admin_router import dashboard_admine
from src.dev.api.routes.whatsapp_router import router as whatsapp_router


#sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
app = FastAPI(debug=True)
# Liste des origines autorisées
origins = [
    "http://************",
    "http://0.0.0.0:55438",
    "http://************:55438",
    "http://************:8000",
    "http://************:8001",
    "http://************:80",
    "http://localhost",
    "http://127.0.0.1",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:8001",
    "http://localhost:54671",
    "http://localhost:60664",
        # Ajoutez ici le port de votre application front-end si nécessaire
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],#origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Configure the logging format to include timestamps
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger("uvicorn")

# Middleware to log requests with more details
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Log the request
        start_time = time.time()

        # Convert the timestamp to a formatted string
        formatted_start_time = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')

        logger.info(f" {formatted_start_time} - X-  Request: {request.method} {request.url} \n  X- Query: {request.query_params}")

        # Call the next middleware or route
        response = await call_next(request)

        # Log the response with timing
        process_time = time.time() - start_time
        logger.info(f"Response: {response.status_code} - Processed in {process_time:.4f}s")

        return response

# Add the logging middleware to the app
app.add_middleware(LoggingMiddleware)




#create DB tables & create extension
Base.metadata.create_all(bind=engine)


# Include routes
app.include_router(auth.router)
app.include_router(base.base_router)
app.include_router(bot_routes.router)
app.include_router(client_routes.client_router)
app.include_router(agent_routes.agent_router)
app.include_router(agent_routes.agents_router)
app.include_router(whatsapp_router)
# app.include_router(visitor_routes.visitor_router)
app.include_router(souscription_routes)

app.include_router(visitor_routes.router)
# app.include_router(souscription.router, prefix="/souscriptions", tags=["Souscriptions"])
app.include_router(pack_routes.router)
app.include_router(payement_routes.router)
app.include_router(user_pack_router)
app.include_router(dashboard_admine)
app.include_router(dashboard_client)

#app.include_router(admin_routes.router)

# Simple test route
@app.get("/")
def read_root():
    return {"Hello": "The main ws "}
