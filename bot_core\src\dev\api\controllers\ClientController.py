from .BaseController import BaseController
from fastapi import UploadFile
from src.dev.models.enums.ResponseEnum import UploadFileResponses
from src.dev.services.rag.upload_file import find_project_root
from src.dev.models.enums.DocumentTypeEnum import DocumentType<PERSON>num
import os
from llama_index.core import SimpleDirectoryReader, Document
import logging
from PyPDF2 import PdfReader # type: ignore
import pandas as pd
import csv
from typing import List
from llama_index.readers.file import PDFReader 
from llama_index.readers.web import BeautifulSoupWebReader 
from pathlib import Path

logger = logging.getLogger(__name__)

class ClientController(BaseController):
    
    def __init__(self):
        super().__init__()
    
    def validate_uploaded_file(self, file: UploadFile):

        if file.content_type not in self.app_settings.FILE_ALLOWED_TYPE:
            return False, UploadFileResponses.FILE_TYPE_NOT_SUPPORTED.value
        
        if file.size > self.app_settings.FILE_SIZE * (1024*1024):
            return False, UploadFileResponses.FILE_SIZE_EXCEEDED.value
        
        else:
            return True, UploadFileResponses.FILE_UPLOAD_SUCCESS.value
    
    def get_client_folder_path(self, client_id: int, bot_id: int):

        root = find_project_root(os.path.dirname(__file__))

        folder_path = os.path.join(
            root,
            "assets/docs",
            str(client_id),
            str(bot_id)
        )
        
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        
        return folder_path
    
    def get_document_reader(self, document_type: str, source: str):
        """
        Read document content and return it in a format compatible with LangChain processor
        """
        try:
            # === Plain file (fallback) ===
            if document_type == DocumentTypeEnum.TYPE_DOC_FILE.value:
                documents = SimpleDirectoryReader(input_files=[source]).load_data()
                return documents if documents else None

            # === PDF ===
            elif document_type == DocumentTypeEnum.TYPE_DOC_PDF.value:
                documents = PDFReader().load_data(Path(source))
                return documents if documents else None

            # === HTML ===
            elif document_type == DocumentTypeEnum.TYPE_DOC_HTML_PAGE.value:
                html_reader = BeautifulSoupWebReader()
                documents = html_reader.load_data([source])
                return documents if documents else None

            else:
                logger.error(f"Unsupported document type: {document_type}")
                return None

        except Exception as e:
            logger.error(f"Error reading document {source}: {e}")
            return None
    
    def _read_html_document(self, source: str):
        """
        Future implementation for reading HTML documents
        """
        # TODO: Implement HTML document reading
        # This could use libraries like BeautifulSoup or requests-html
        pass
    
    def get_supported_file_types(self):
        """
        Return list of supported file types
        """
        return self.app_settings.FILE_ALLOWED_TYPE
    
    def get_max_file_size(self):
        """
        Return maximum file size in MB
        """
        return self.app_settings.FILE_SIZE
    
    def validate_file_extension(self, filename: str):
        """
        Validate file extension
        """
        allowed_extensions = ['.txt', '.pdf', '.docx', '.doc', '.md']
        file_extension = os.path.splitext(filename)[1].lower()
        return file_extension in allowed_extensions
