from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timedelta
from src.dev.utils.dependencies import get_db
from src.dev.models.payment_model import Payment  # Importation correcte
from src.dev.models.user_model import Client
from src.dev.models.subscription_model import Subscription
from src.dev.models.question_response_model import QuestionResponseLog
from src.dev.models.bot_model import Bot
from src.dev.models.pack_model import Pack

dashboard_admine = APIRouter()

@dashboard_admine.get("/api/v1/dashboard_admine/stats")
def get_admin_stats(date_filter: str, db: Session = Depends(get_db)):
    if date_filter not in ['day', 'month', 'year']:
        raise HTTPException(status_code=400, detail="Invalid date filter")

    now = datetime.now()
    if date_filter == 'day':
        start_date = now - timedelta(days=1)
    elif date_filter == 'month':
        start_date = now - timedelta(days=30)
    elif date_filter == 'year':
        start_date = now - timedelta(days=365)

    # Calcul des statistiques demandées
    total_bots = db.query(Bot).count()
    total_questions = db.query(QuestionResponseLog).filter(QuestionResponseLog.timeQuestion >= start_date).count()
    total_responses = db.query(QuestionResponseLog).filter(QuestionResponseLog.timeQuestion >= start_date).count()
    active_subscriptions = db.query(Subscription).filter(Subscription.startDate >= start_date, Subscription.status == "1").count()
    packs_subscribed_raw = (
    db.query(Pack.packName, func.count(Subscription.idPack))  # Sélectionner nom_du_pack et le compte
    .join(Subscription, Subscription.idPack == Pack.idPack)  # Jointure entre Subscription et Pack
    .filter(Subscription.status == "1")  # Filtrer pour inclure uniquement les enregistrements où le statut est "1"
    .group_by(Pack.packName)  # Grouper par nom_du_pack
    .all()
)

# Convertir le résultat en un dictionnaire
    packs_subscribed = {pack: count for pack, count in packs_subscribed_raw}
    active_clients = db.query(Client).count()
    bots_per_client = (
        db.query(Subscription.userId, func.count(Bot.idBot))
        .join(Bot, Bot.idSubscription == Subscription.idSubscription)
        .group_by(Subscription.userId)  # Correction pour regrouper par userId
        .all()
    )
    bots_per_client_dict = {
        f"Client_id {user_id}": count
        for user_id, count in bots_per_client
    }

    revenue_generated = db.query(func.sum(Payment.amount)).filter(Payment.timestamp >= start_date).scalar()

    # Structure de la réponse
    response = {
        "total_bots": total_bots,
        "total_questions": total_questions,
        "total_responses": total_responses,
        "active_subscriptions": active_subscriptions,
        "packs_subscribed": packs_subscribed,
        "active_clients": active_clients,
        "bots_per_client": bots_per_client_dict,
        "revenue_generated": revenue_generated or 0  # Si aucun paiement n'est trouvé, renvoyer 0
    }

    return response
