from typing import Annotated

from fastapi import Depends, status
from src.dev.services.auth.auth import get_current_user
from src.dev.schemas.auth import UserSchema
from src.dev.exc import raise_with_log


class RoleChecker:  
  def __init__(self, allowed_roles):  
    self.allowed_roles = allowed_roles  
  
  def __call__(self, user:UserSchema = Depends(get_current_user)):  
    if user.type in self.allowed_roles:  
      return user  
    raise_with_log(status.HTTP_401_UNAUTHORIZED, "Vous n'avez pas assez d'autorisations")


