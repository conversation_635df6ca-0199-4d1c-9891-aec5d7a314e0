from pydantic import BaseModel, EmailStr
from src.dev.models.enums.roleEnum import Agent<PERSON><PERSON>
from pydantic import BaseModel, ConfigDict

class CreateAgentSchema(BaseModel):
    username: str
    email: EmailStr
    password: str
    additional_info: str | None = None
    role: AgentRole
    is_assigned: bool = False

    class Config:
        from_attributes = True


class AgentSchema(BaseModel):
    userId: int
    username: str | None = None
    email: EmailStr | None = None
    additional_info: str | None = None

    role: AgentRole
    is_assigned: bool = False
    model_config = ConfigDict(from_attributes=True)

class AgentUpdate(BaseModel):
    email: EmailStr | None = None
    role: AgentRole | None = None
    is_assigned: bool | None = None