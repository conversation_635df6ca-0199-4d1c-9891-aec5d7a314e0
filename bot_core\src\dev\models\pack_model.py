from sqlalchemy import Column, Integer, String, BigInteger, Float
from sqlalchemy.orm import relationship
from src.dev.utils.database import Base


class Pack(Base):
    __tablename__ = 'pack'  # The name of the table in the database

    idPack = Column(Integer, primary_key=True, index=True, autoincrement=True)
    packName = Column(String(20), nullable=True)
    maxInputToken = Column(BigInteger, nullable=True)
    price = Column(Float, nullable=True)
    maxOutputToken = Column(BigInteger, nullable=True)

    # Relationship with the Subscription model
    subscriptions = relationship("Subscription", back_populates="pack")
    