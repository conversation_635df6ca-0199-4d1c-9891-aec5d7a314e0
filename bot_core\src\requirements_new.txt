aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.13
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.8.0
attrs==25.3.0
banks==2.1.2
bcrypt==3.2.2
beautifulsoup4==4.13.3
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
chromedriver-autoinstaller==0.6.4
click==8.1.8
colorama==0.4.6
cryptography==44.0.2
cssselect==1.3.0
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.7.0
ecdsa==0.19.1
einops==0.8.1
elastic-transport==8.17.1
elasticsearch==8.17.2
email_validator==2.2.0
et_xmlfile==2.0.0
faiss-cpu==1.11.0
fastapi==0.115.11
feedfinder2==0.0.4
feedparser==6.0.11
filelock==3.18.0
filetype==1.2.0
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
griffe==1.7.3
h11==0.14.0
html2text==2024.2.26
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.3
idna==3.10
jieba3k==0.35.1
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.26
langchain-community==0.3.26
langchain-core==0.3.66
langchain-text-splitters==0.3.8
langdetect==1.0.9
langsmith==0.4.1
llama-cloud==0.1.14
llama-cloud-services==0.6.5
llama-index==0.12.35
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.35
llama-index-embeddings-huggingface==0.5.2
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.9
llama-index-llms-ollama==0.5.3
llama-index-llms-openai==0.3.25
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.6
llama-index-readers-llama-parse==0.4.0
llama-index-readers-web==0.3.8
llama-parse==0.6.4.post1
lxml==5.3.1
MarkupSafe==3.0.2
marshmallow==3.26.1
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
newspaper3k==0.2.8
nltk==3.9.1
numpy==2.2.3
ollama==0.4.7
openai==1.66.3
openpyxl==3.1.5
orjson==3.10.18
outcome==1.3.0.post0
packaging==24.2
pandas==2.2.3
passlib==1.7.4
pdfminer.six==20231228
pdfplumber==0.11.5
pgvector==0.3.6
pillow==11.1.0
pip==25.0.1
platformdirs==4.3.8
playwright==1.50.0
propcache==0.3.0
psycopg2==2.9.10
psycopg2-binary==2.9.10
pyasn1==0.4.8
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pydantic-settings==2.8.1
pyee==12.1.1
pypdf==5.3.1
PyPDF2==3.0.1
pypdfium2==4.30.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-jose==3.4.0
python-multipart==0.0.20
pytz==2025.1
PyYAML==6.0.2
rank-bm25==0.2.2
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-toolbelt==1.0.0
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
selenium==4.29.0
sentence-transformers==3.4.1
setuptools==76.0.0
sgmllib3k==1.0.0
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
spider-client==0.0.27
SQLAlchemy==2.0.39
starlette==0.46.1
striprtf==0.0.26
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tinysegmenter==0.3
tldextract==5.1.3
tokenizers==0.21.1
torch==2.6.0
tqdm==4.67.1
transformers==4.49.0
trio==0.29.0
trio-websocket==0.12.2
typing_extensions==4.12.2
typing-inspect==0.9.0
tzdata==2025.1
Unidecode==1.3.8
urllib3==2.3.0
uvicorn==0.34.0
websocket-client==1.8.0
wrapt==1.17.2
wsproto==1.2.0
yarl==1.18.3
zstandard==0.23.0